//var BASE_URL = "https://51erp.store/xace-web/api/amazon";
 var BASE_URL = "http://127.0.0.1:32000/api/amazon";

var WAITGET_URL = BASE_URL + "/list/task2/waitGets";
var POSTOFFER_URL = BASE_URL + "/collect2/putOfferJson";
var BATCH_URL = BASE_URL + "/collect/batch";
var NEW_BATCH_URL = BASE_URL + "/collect2/newSellerCollect";
var REMOVE_URL = BASE_URL + "/collect/remove";

var CATE_WAITGET_URL = BASE_URL + "/task/waitGets";
var CATE_POSTOFFER_URL = BASE_URL + "/task/putTaskJson";

// Amazon产品详情采集相关API
var DETAIL_TASK_WAITGET_URL = BASE_URL + "/detailTask/waitGets";
var DETAIL_TASK_SUBMIT_URL = BASE_URL + "/detailTask/submitResult";
var SPU_SUBMIT_URL = BASE_URL + "/spu/submit";
var SKU_SUBMIT_URL = BASE_URL + "/sku/submit";

var uniqueId = "";
let lastUpdateTime = Date.now();


function util_getscript_sellerData_content(html = "") {
  var scriptTagPattern = /<script\b[^>]*>([\s\S]*?)<\/script>/gm;
  var matches = html.match(scriptTagPattern);
  if (matches) {
    for (var i = 0; i < matches.length; i++) {
      var scriptContent = matches[i].replace(scriptTagPattern, "$1");
      if (
        scriptContent.indexOf("forceBuyNow") != -1 &&
        scriptContent.indexOf("offer") != -1 &&
        scriptContent.indexOf("seller") != -1 &&
        scriptContent.indexOf("company") != -1
      ) {
        return scriptContent;
      }
    }
  }
  return false;
}

function util_get_random_number(min = 1000, max = 5000) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

function util_getscript_genmaiData_content(html = "") {
  var scriptTagPattern = /<script\b[^>]*>([\s\S]*?)<\/script>/gm;
  var matches = html.match(scriptTagPattern);
  if (matches) {
    for (var i = 0; i < matches.length; i++) {
      var scriptContent = matches[i].replace(scriptTagPattern, "$1");
      if (
        scriptContent.indexOf("ata-analytics-view-custom-cheapest-price") != -1
      ) {
        return scriptContent;
      }
    }
  }
  return false;
}

function util_checkFanpa(str = "") {
  const searchString = "This item is sold out";
  const regex = new RegExp(searchString);
  return regex.test(str);
}

function pause_s_time() {
  chrome.storage.local.set({ pause_s_time: Date.now() });
}

// 获取 HTML 中指定选择器对应元素的文本内容
function extractTextFromHTML(html, selector) {
  const regex = new RegExp(`<${selector}[^>]*>([^<]*)<\/${selector}>`, "g");
  const matches = [];
  let match;
  while ((match = regex.exec(html)) !== null) {
    matches.push(match[1]);
  }
  return matches.length > 0 ? matches.join(" ") : "";
}

async function request_offer() {
  console.log(ct(), "后台请求offer详情");

  chrome.storage.local.get(["uniqueId"], function (result) {
    if (!result.hasOwnProperty("uniqueId")) {
      uniqueId = generateUniqueId();
      chrome.storage.local.set({ uniqueId: uniqueId }, function () {
        console.log(ct(), "Unique ID is set to " + uniqueId);
      });
    } else {
      uniqueId = result.uniqueId;
    }

    // 请求接口获取数据
    fetch(WAITGET_URL, {
      method: "GET",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
      },
    })
      .then((response) => response.json())
      .then((data) => {
        if (data.code === 200) {
          console.log(ct(), "请求到：waitGets数据。" + data.data.length);
       
          if (data.data.length == 0) {
            //等待1个小时
            console.log(
              ct(),
              "===============后台没有待处理数据，等待15分钟=================="
            );
            setTimeout(pause_s_time, 900000); // 15分钟 = 15 * 60 * 1000 毫秒
          }

          isRequest = true;

          data.data.forEach((item) => {
            console.log(
              ct(),
              ".....加载：" + item.offerLink + ", ID:" + item.id
            );
            let key = "request_offer_detail_" + item.id;
            let obj = {
              [key]: { link: item.offerLink, time: new Date().getTime() },
            };
            chrome.storage.local.set(obj);

            const requestOptions = {
              method: 'GET',
              headers: {
                'accept': 'application/json, text/javascript, */*; q=0.01',
                'Content-Type': 'application/json'
              },  
            };     

            fetch(item.offerLink,requestOptions)
              .then((response) => {
                console.log(ct(), "响应状态码：" + response.status); // 打印状态码
                if (response.status === 403) {
                  // 如果状态码是403，等待
                  pause_s_time();
                }
                if (response.status === 404) {
                  let offerRemove = {
                    cpId: item.id,
                    clientId: uniqueId,
                    reason:  "404错误"
                  };

                  // 如果状态码是404，发送cpId到REMOVE_URL并停止处理
                  console.log(
                    ct(),
                    `链接 ${item.offerLink} 返回404，将cpId${item.id} 报送给REMOVE_URL`
                  );
                  fetch(REMOVE_URL, {
                    method: "PUT",
                    headers: {
                      Accept: "application/json",
                      "Content-Type": "application/json",
                    },
                    body: JSON.stringify(offerRemove),
                  })
                    .then((removeResponse) => removeResponse.json())
                    .then((removeData) => {
                      console.log(ct(), "REMOVE_URL 响应:", removeData);
                      // 这里可以添加额外的逻辑来处理REMOVE_URL的响应
                    })
                    .catch((removeError) => {
                      erpErrLog("发送到REMOVE_URL时出错:", removeError);
                    });
                  // 不再继续执行下面的代码，因为链接是404
                  return;
                }
                // 如果状态码不是404，继续处理响应体
                return response.json();
              })
              .then((data) => {
                // console.log(ct(), "数据抓取成功:", data);



                if (!data) {
                  // 如果data是undefined，不执行任何操作或进行错误处理
                  return;
                }
                var productHeader = {};
                if (data["allegro.showoffer.productHeader"] != undefined)
                    productHeader = data["allegro.showoffer.productHeader"];
                else
                    productHeader = data["showoffer.productHeader"];



                if(productHeader["offer"]["view"]["type"]==("ENDED")){
                  // 如果碰到销售结束，则删除数据
                  let offerRemove = {
                    cpId: item.id,
                    clientId: uniqueId,
                    reason:  "销售结束"
                  };

                  // 如果状态码是404，发送cpId到REMOVE_URL并停止处理
                  console.log(
                    ct(),
                    `链接 ${item.offerLink} 销售结束，将cpId${item.id} 报送给REMOVE_URL`
                  );
                  fetch(REMOVE_URL, {
                    method: "PUT",
                    headers: {
                      Accept: "application/json",
                      "Content-Type": "application/json",
                    },
                    body: JSON.stringify(offerRemove),
                  })
                    .then((removeResponse) => removeResponse.json())
                    .then((removeData) => {
                      console.log(ct(), "REMOVE_URL 响应:", removeData);
                      // 这里可以添加额外的逻辑来处理REMOVE_URL的响应
                    })
                    .catch((removeError) => {
                      erpErrLog("发送到REMOVE_URL时出错:", removeError);
                    });
                  // 不再继续执行下面的代码，因为链接是404
                  return;

                }



             
                  if (productHeader) {

                    let offer = {
                      cpId: item.id,
                      offerJson: productHeader,
                      customJson:{},
                      clientId: uniqueId,
                    };

                    fetch(POSTOFFER_URL, {
                      method: "PUT",
                      headers: {
                        Accept: "application/json",
                        "Content-Type": "application/json",
                      },
                      body: JSON.stringify(offer),
                    })
                      .then((response) => response.json())
                      .then((data) => {
                        console.log(ct(), "数据保存成功:", data.msg);
                      })
                      .catch((error) => {
                        erpErrLog("数据保存失败:", error);
                      });
                  } else {
                    pause_s_time();
                    sendPopTips(
                      "遇到反爬,请开启或者更换代理或者暂停采集，稍后再采集!"
                    );
                  }
                
                chrome.storage.local.remove(key, function () {
                  console.log(ct(), `键名 ${key} 的值已被清空！`);
                });
              })
              .catch((error) => {
                chrome.storage.local.remove(key, function () {
                  erpErrLog(`数据抓取错误 ，键名 ${key} 的值已被清空！`, error);
                });
                pause_s_time();
                sendPopTips(
                  "遇到反爬,请验证,请开启或者更换代理或者暂停采集，稍后再采集!"
                );
              });
          });
        } else {
          erpErrLog("51ERP API请求失败:", data.msg);
        }
      })
      .catch((error) => {
        erpErrLog("请求错误:", error);
      });
  });
}

function request_shangjia_company_page_link() {
  chrome.storage.local.get(null, function (items) {
    let need_item = null;
    for (let key in items) {
      if (key.indexOf("shangjia_about_contact_and_basic_info_") != -1) {
        if (
          items[key]["_is_request"] == 2 &&
          items[key]["_seller_page_url"] &&
          items[key]["_seller_page_url_is_request"] == 0
        ) {
          need_item = items[key];
          need_item["_seller_page_url_is_request"] = 1;
          need_item["_seller_page_url_s_time"] = Date.now();
          let obj = {};
          obj[key] = need_item;
          chrome.storage.local.set(obj, function () {
            request_shangjia_company_page_link_ajax(key);
          });
          break;
        }
      }
    }
  });
}

function xintiao() {
  chrome.storage.local.get(null, function (items) {
    if (!items.hasOwnProperty("pause") || !items["pause"]) {
      if (
        items.hasOwnProperty("pause_s_time") &&
        items["pause_s_time"] + 600000 > Date.now()
      ) {
        console.log(ct(), "后台详情爬取失败，暂停10分钟");
      } else {
        console.log(ct(), "后台详情爬取中:");
        request_shangjia_about_contact_and_basic_info();
      }
    } else {
      console.log(ct(), "=========后台详情已暂停爬取================");
    }
  });

  // 20240731 新增
  if (Date.now() - lastUpdateTime > 3 * 60 * 1000) { // 3分钟 content.js 没动作，需要刷新一下前端活动页面。
    // chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
    //     chrome.tabs.reload(tabs[0].id);
    // });

     chrome.tabs.query({currentWindow: true}, function(tabs) {
            tabs.forEach(function(tab) {
                // 可以在这里添加条件判断，只刷新特定标签页
                // 例如，只刷新包含特定网址的标签页
                 if (tab.url && tab.url.includes('amazon.com')) {
                    chrome.tabs.reload(tab.id);
                 }
            });
        });
        lastUpdateTime = Date.now() ; // 重置时间更新
    
    

}
}

function request_shangjia_about_contact_and_basic_info() {
  chrome.storage.local.get(null, function (items) {
    let binfa = 0;
    for (let key in items) {
      if (key.indexOf("request_offer_detail_") != -1) {
        if (items[key]["time"] + 300000 > Date.now()) {
          chrome.storage.local.remove(key, function () {
            console.log(ct(), `键名 ${key} 的值已被清空,5分钟未处理的数据！`);
          });
        }
        binfa++;
        if (binfa == 20) {
          break;
        }
      }
    }
    if (binfa < 20) {
      console.log(ct(), "当前并发数：" + binfa);
      request_offer();
    }
  });
}

function storage_shangjias_update(taskType,shangjia_infos = []) {
  chrome.storage.local.get(null, function (items) {
    let total_add_shangjia_num = items.hasOwnProperty("total_add_shangjia_num")
      ? items["total_add_shangjia_num"]
      : 0;
    total_add_shangjia_num += shangjia_infos.length;
    chrome.storage.local.set({
      total_add_shangjia_num: total_add_shangjia_num,
    });

    sendPutRequest(taskType,shangjia_infos)
      .then((responseData) => {
        console.log(ct(), "成功提交到后台:", responseData);
      })
      .catch((error) => {
        erpErrLog("Fetch error details:", error);
        erpErrLog("Error stack trace:", error.stack);
      });
  });
}

function sendPutRequest(taskType,data, retries = 3) {
  const headers = {
    "Content-Type": "application/json",
  };
  return new Promise((resolve, reject) => {
    function attempt(remainingRetries) {
      fetch(NEW_BATCH_URL+"/"+taskType, {
        method: "PUT",
        headers: headers,
        body: JSON.stringify(data),
      })
        .then((response) => response.json())
        .then(resolve)
        .catch((error) => {
          erpErrLog(`Attempt failed: ${error}`);
          if (remainingRetries > 0) {
            erpErrLog(
              `重试中... (${retries - remainingRetries + 1}/${retries})`
            );
            setTimeout(() => attempt(remainingRetries - 1), 20000);
            sendPopTips(
              "网络错误，请检查网络连接,数据提交不成功，请确认是否使用代理导致的"
            );
          } else {
            reject(new Error(`HTTP error! status: ${error}`));
          }
        });
    }
    attempt(retries);
  });
}

function storage_get(callback) {
  chrome.storage.local.get(null, callback);
}

chrome.runtime.onMessage.addListener(function (request, sender, sendResponse) {
  if (request.action === "updateTime") {
    lastUpdateTime = Date.now();
    sendResponse({state: "time updated："+new Date(lastUpdateTime).toLocaleString()});
}else  if (request.action == "shangjia_items_update") {
    console.log("action", request, sender, sendResponse);
    let shangjia_infos = request.shangjia_infos;
    storage_shangjias_update(request.taskType, shangjia_infos);
    sendResponse({ state: "商品保存成功！" });
  } else if (request.action == "can_zidong_caiji") {
    chrome.storage.local.get(null, function (items) {
      if (!items.hasOwnProperty("pause_page") || items["pause_page"] == 0) {
        let total_add_shangjia_num = items.hasOwnProperty(
          "total_add_shangjia_num"
        )
          ? items["total_add_shangjia_num"]
          : 0;

        uniqueId = items.hasOwnProperty("uniqueId") ? items["uniqueId"] : "";
        console.log(ct(), "类目采集： uniqueId:" + uniqueId);

        var collect_item_num = items["collect_item_num"] || 10000;
        let msg = {
          state: 0,
          collect_item_num: collect_item_num,
          collect_zero_sales: items["collect_zero_sales"] || false,
          collect_min_price: items["collect_min_price"] || 50,
          collect_max_price: items["collect_max_price"] || 500,
          collect_max_page: items["collect_max_page"] || 100,
          uniqueId: uniqueId,
        };
        sendResponse(JSON.stringify(msg));
        

        // if (total_add_shangjia_num <= collect_item_num) {
          
        // } else {
        //   sendPopTips("商品达到上限，停止收集！");
        //   sendResponse(JSON.stringify({ state: 1 }));
        // }
      } else {
        sendResponse(JSON.stringify({ state: 1 }));
      }
    });
    return true;
  } else if (request.action == "start_detail_crawling") {
    // 启动Amazon产品详情采集
    chrome.storage.local.get(["uniqueId"], function (items) {
      const uniqueId = items.uniqueId || generateUniqueId();
      chrome.storage.local.set({ uniqueId: uniqueId });

      // 启动采集循环（在后台运行）
      startAmazonDetailCrawlingLoop(uniqueId);

      sendResponse({ success: true, message: "Amazon产品详情采集已启动" });
    });
    return true;
  } else if (request.action == "process_single_product") {
    // 处理单个产品详情
    chrome.storage.local.get(["uniqueId"], async function (items) {
      const uniqueId = items.uniqueId || generateUniqueId();

      try {
        const success = await processAmazonProductDetail(
          request.productUrl,
          request.taskId || 0,
          uniqueId
        );
        sendResponse({ success: success });
      } catch (error) {
        sendResponse({ success: false, error: error.message });
      }
    });
    return true;
  }
});

// 这个函数用于发送消息到弹窗
function sendPopTips(msg = "", url = "") {
  // 寻找一个连接是来自弹窗的端口
  // let popupPort = null;
  // chrome.runtime_ports.forEach((port) => {
  //     if (port.name === "popup") {
  //         popupPort = port;
  //     }
  // });
  // if (popupPort) {
  //     popupPort.postMessage({ action: "background-tips", info: msg, url: url });
  // } else {
  //     console.error("No popup port found");
  //     // 可能需要打开弹窗或创建连接
  // }
}

// ==================== Amazon产品详情采集系统 ====================

/**
 * 获取待处理的Amazon产品详情采集任务
 * @param {string} clientId 客户端ID
 * @returns {Promise<Object|null>} 任务对象或null
 */
async function getPendingDetailTask(clientId) {
  try {
    const url = `${DETAIL_TASK_WAITGET_URL}?clientId=${clientId}`;
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`获取详情采集任务失败，状态码: ${response.status}`);
    }

    const data = await response.json();
    console.log("获取到详情采集任务:", data);

    if (data.success && data.data) {
      return data.data;
    }

    return null;
  } catch (error) {
    console.error("获取详情采集任务失败:", error);
    return null;
  }
}

/**
 * 提交SPU数据到后台
 * @param {Object} spuData SPU数据对象
 * @returns {Promise<boolean>} 是否成功
 */
async function submitSpuData(spuData) {
  try {
    const response = await fetch(SPU_SUBMIT_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(spuData),
    });

    if (!response.ok) {
      throw new Error(`提交SPU数据失败，状态码: ${response.status}`);
    }

    const result = await response.json();
    console.log("SPU数据提交成功:", result);
    return true;
  } catch (error) {
    console.error("提交SPU数据失败:", error);
    return false;
  }
}

/**
 * 批量提交SKU数据到后台
 * @param {Array} skuDataList SKU数据数组
 * @returns {Promise<boolean>} 是否成功
 */
async function submitSkuDataBatch(skuDataList) {
  try {
    const response = await fetch(SKU_SUBMIT_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ skuList: skuDataList }),
    });

    if (!response.ok) {
      throw new Error(`批量提交SKU数据失败，状态码: ${response.status}`);
    }

    const result = await response.json();
    console.log(`批量提交${skuDataList.length}个SKU数据成功:`, result);
    return true;
  } catch (error) {
    console.error("批量提交SKU数据失败:", error);
    return false;
  }
}

/**
 * 标记详情采集任务完成
 * @param {number} taskId 任务ID
 * @param {boolean} success 是否成功
 * @param {string} errorMessage 错误信息（如果失败）
 * @returns {Promise<boolean>} 是否成功
 */
async function markDetailTaskCompleted(taskId, success = true, errorMessage = '') {
  try {
    const payload = {
      taskId: taskId,
      status: success ? 'completed' : 'failed',
      errorMessage: errorMessage,
      completedAt: new Date().toISOString()
    };

    const response = await fetch(DETAIL_TASK_SUBMIT_URL, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      throw new Error(`标记任务完成失败，状态码: ${response.status}`);
    }

    const result = await response.json();
    console.log("任务状态更新成功:", result);
    return true;
  } catch (error) {
    console.error("标记任务完成失败:", error);
    return false;
  }
}

/**
 * 从Amazon页面提取SPU数据
 * @param {Document} doc 页面文档对象
 * @param {string} pageUrl 页面URL（用于提取ASIN）
 * @returns {Object} SPU数据对象
 */
function extractSpuData(doc, pageUrl = '') {
  console.log("开始提取SPU数据...");

  const spuData = {};

  // 1. 提取ASIN（优先使用parentAsin）
  let parentAsin = null;
  const scripts = doc.querySelectorAll('script');

  for (const script of scripts) {
    if (script.textContent && script.textContent.includes('parentAsin')) {
      const parentMatch = script.textContent.match(/"parentAsin"\s*:\s*"([^"]+)"/);
      if (parentMatch) {
        parentAsin = parentMatch[1];
        break;
      }
    }
  }

  if (parentAsin) {
    spuData.asin = parentAsin;
    console.log("使用parentAsin作为SPU主键:", parentAsin);
  } else {
    // 从页面元素或URL中提取ASIN
    const asinElement = doc.querySelector('[data-asin]');
    if (asinElement) {
      spuData.asin = asinElement.getAttribute('data-asin');
    } else {
      // 从URL中提取ASIN
      const urlMatch = pageUrl.match(/\/dp\/([A-Z0-9]{10})/);
      spuData.asin = urlMatch ? urlMatch[1] : 'UNKNOWN';
    }
    console.log("使用当前页面ASIN作为SPU主键:", spuData.asin);
  }

  // 2. 提取标题
  const titleElement = doc.querySelector('#productTitle');
  spuData.title = titleElement ? titleElement.textContent.trim() : null;

  // 3. 提取品牌
  const brandElement = doc.querySelector('#bylineInfo');
  if (brandElement) {
    const brandText = brandElement.textContent.trim();
    const brandMatch = brandText.match(/Visit the (.+?) Store/);
    spuData.brand = brandMatch ? brandMatch[1] : brandText;
  } else {
    spuData.brand = null;
  }

  // 4. 提取评分
  const ratingElement = doc.querySelector('.a-icon-star-mini .a-icon-alt, .a-icon-star .a-icon-alt');
  if (ratingElement) {
    const ratingText = ratingElement.textContent.trim();
    const ratingMatch = ratingText.match(/(\d+\.?\d*)/);
    spuData.rating = ratingMatch ? parseFloat(ratingMatch[1]) : null;
  } else {
    spuData.rating = null;
  }

  // 5. 提取评价数量
  const reviewElement = doc.querySelector('#acrCustomerReviewText');
  if (reviewElement) {
    const reviewText = reviewElement.textContent.trim();
    const reviewMatch = reviewText.replace(/,/g, '').match(/(\d+)/);
    spuData.reviewCount = reviewMatch ? parseInt(reviewMatch[1]) : null;
  } else {
    spuData.reviewCount = null;
  }

  // 6. 提取主图URL
  const mainImageElement = doc.querySelector('#landingImage, #imgBlkFront');
  if (mainImageElement) {
    spuData.mainImageUrl = mainImageElement.src || mainImageElement.getAttribute('data-src');
  } else {
    spuData.mainImageUrl = null;
  }

  // 7. 提取所有图片URLs
  const imageUrls = [];
  const imageElements = doc.querySelectorAll('#altImages img, .a-carousel img');
  for (const img of imageElements) {
    const imgUrl = img.src || img.getAttribute('data-src');
    if (imgUrl && imgUrl.includes('amazon.com')) {
      imageUrls.push(imgUrl);
    }
  }
  spuData.imageUrls = JSON.stringify(imageUrls.slice(0, 10)); // 最多保存10张图片

  // 8. 提取五点描述
  const bulletPoints = [];
  const bulletElements = doc.querySelectorAll('#featurebullets_feature_div li span.a-list-item');
  for (const bullet of bulletElements) {
    const text = bullet.textContent.trim();
    if (text && text.length > 10) { // 过滤掉太短的文本
      bulletPoints.push(text);
    }
  }
  spuData.bulletPoints = JSON.stringify(bulletPoints.slice(0, 5)); // 最多保存5个要点

  // 9. 提取商品描述
  const descElement = doc.querySelector('#productDescription, #aplus');
  spuData.description = descElement ? descElement.textContent.trim().substring(0, 2000) : null;

  // 10. 提取产品详情和技术规格
  const productDetails = {};
  const productAttributes = {};

  // 从产品详情表格中提取
  const detailSelectors = [
    '#prodDetails table.prodDetTable tr',
    '#productDetails_detailBullets_sections1 tr',
    '#productDetails_expanderTables tr',
    '#technicalSpecifications_section_1 tr'
  ];

  for (const selector of detailSelectors) {
    const detailElements = doc.querySelectorAll(selector);
    for (const row of detailElements) {
      const cells = row.querySelectorAll('td');
      let key, value;

      if (cells.length >= 2) {
        key = cells[0].textContent.trim();
        value = cells[1].textContent.trim();
      } else {
        const thElement = row.querySelector('th');
        const tdElement = row.querySelector('td');
        if (thElement && tdElement) {
          key = thElement.textContent.trim();
          value = tdElement.textContent.trim();
        } else {
          continue;
        }
      }

      if (key && value && key !== 'Customer Reviews') {
        key = key.replace(':', '').trim();
        productDetails[key] = value;
        productAttributes[key] = value;
      }
    }
  }

  spuData.productDetails = JSON.stringify(productDetails);
  spuData.productAttributes = JSON.stringify(productAttributes);

  // 11. 提取类目路径
  const breadcrumbElements = doc.querySelectorAll('#wayfinding-breadcrumbs_feature_div a');
  const categoryPath = [];
  for (const breadcrumb of breadcrumbElements) {
    const text = breadcrumb.textContent.trim();
    if (text && text !== 'Home' && text !== 'Amazon.com') {
      categoryPath.push(text);
    }
  }
  spuData.categoryPath = categoryPath.join(' > ');

  // 12. 组装Temu格式的产品详情
  const temuProductDetail = {
    basic_info: {
      title: spuData.title || '',
      brand: spuData.brand || '',
      rating: spuData.rating,
      review_count: spuData.reviewCount
    },
    media: {
      main_image: spuData.mainImageUrl || '',
      images: JSON.parse(spuData.imageUrls || '[]')
    },
    description: {
      bullet_points: JSON.parse(spuData.bulletPoints || '[]'),
      long_description: spuData.description || ''
    },
    specifications: productAttributes,
    category: {
      path: spuData.categoryPath || ''
    }
  };

  spuData.temuProductDetail = JSON.stringify(temuProductDetail);

  console.log("SPU数据提取完成:", spuData.asin, "-", spuData.title?.substring(0, 50) + "...");
  return spuData;
}

/**
 * 从JavaScript数据中提取变体信息
 * @param {Document} doc 页面文档对象
 * @returns {Object} 变体数据对象
 */
function extractVariationData(doc) {
  console.log("开始提取变体数据...");

  const variationData = {};
  const scripts = doc.querySelectorAll('script');

  for (const script of scripts) {
    const scriptContent = script.textContent;
    if (!scriptContent || !scriptContent.includes('dimensionToAsinMap')) {
      continue;
    }

    try {
      // 提取dimensionToAsinMap
      const dimensionMatch = scriptContent.match(/"dimensionToAsinMap"\s*:\s*({[^}]+})/);
      if (dimensionMatch) {
        variationData.dimensionToAsinMap = JSON.parse(dimensionMatch[1]);
        console.log("找到dimensionToAsinMap:", variationData.dimensionToAsinMap);
      }

      // 提取variationValues
      const variationMatch = scriptContent.match(/"variationValues"\s*:\s*({[^}]+})/);
      if (variationMatch) {
        variationData.variationValues = JSON.parse(variationMatch[1]);
        console.log("找到variationValues:", variationData.variationValues);
      }

      // 提取colorToAsin
      const colorToAsinMatch = scriptContent.match(/"colorToAsin"\s*:\s*({[^}]+})/);
      if (colorToAsinMatch) {
        variationData.colorToAsin = JSON.parse(colorToAsinMatch[1]);
        console.log("找到colorToAsin:", variationData.colorToAsin);
      }

      // 提取colorImages
      const colorImagesMatch = scriptContent.match(/"colorImages"\s*:\s*({[\s\S]*?})\s*,\s*"heroImages"/);
      if (colorImagesMatch) {
        try {
          variationData.colorImages = JSON.parse(colorImagesMatch[1]);
          console.log("找到colorImages，变体数量:", Object.keys(variationData.colorImages).length);
        } catch (e) {
          console.warn("解析colorImages失败:", e);
        }
      }

      // 提取parentAsin
      const parentMatch = scriptContent.match(/"parentAsin"\s*:\s*"([^"]+)"/);
      if (parentMatch) {
        variationData.parentAsin = parentMatch[1];
        console.log("找到parentAsin:", parentMatch[1]);
      }

      break; // 找到数据后退出循环
    } catch (error) {
      console.warn("解析变体数据时出错:", error);
      continue;
    }
  }

  return variationData;
}

/**
 * 判断是否为多变体产品
 * @param {Object} variationData 变体数据
 * @returns {boolean} 是否为多变体
 */
function isMultiVariantProduct(variationData) {
  const hasMultipleDimensions = variationData.dimensionToAsinMap &&
                                Object.keys(variationData.dimensionToAsinMap).length > 1;
  const hasMultipleColors = variationData.colorToAsin &&
                           Object.keys(variationData.colorToAsin).length > 1;

  return hasMultipleDimensions || hasMultipleColors;
}

/**
 * 从变体数据中提取所有SKU信息
 * @param {Object} variationData 变体数据
 * @param {string} parentAsin 父ASIN
 * @returns {Array} SKU数据数组
 */
function extractSkuDataFromVariations(variationData, parentAsin) {
  console.log("开始从变体数据提取SKU信息...");

  const skuDataList = [];

  if (!variationData.dimensionToAsinMap) {
    console.log("未找到变体数据，将创建单个SKU");
    return [];
  }

  const dimensionMap = variationData.dimensionToAsinMap;
  const variationValues = variationData.variationValues || {};
  const colorToAsin = variationData.colorToAsin || {};
  const colorImages = variationData.colorImages || {};

  // 获取变体属性名称列表
  const sizeNames = variationValues.size_name || [];
  const colorNames = variationValues.color_name || [];

  for (const [index, asin] of Object.entries(dimensionMap)) {
    const skuData = {
      asin: asin,
      parentAsin: parentAsin,
      currency: 'USD',
      stockStatus: 'In Stock',
      price: null,
      imageUrl: null,
      variationAttributes: {}
    };

    // 解析变体属性
    const variationAttributes = {};
    let displayName = `Variant ${index}`;

    try {
      // 检查是否为多维度变体索引（如"0_0", "1_3"）
      if (index.includes('_')) {
        const parts = index.split('_');
        if (parts.length >= 2) {
          const sizeIndex = parseInt(parts[0]);
          const colorIndex = parseInt(parts[1]);

          if (sizeIndex < sizeNames.length) {
            variationAttributes.Size = sizeNames[sizeIndex];
          }

          if (colorIndex < colorNames.length) {
            variationAttributes.Color = colorNames[colorIndex];
          }

          displayName = `${variationAttributes.Color || 'Unknown'} - ${variationAttributes.Size || 'Unknown'}`;
        }
      } else {
        // 单维度变体：尝试解析为颜色索引
        const colorIndex = parseInt(index);
        if (colorIndex < colorNames.length) {
          variationAttributes.Color = colorNames[colorIndex];
          displayName = colorNames[colorIndex];
        }
      }
    } catch (error) {
      console.warn("解析变体属性失败:", error);
    }

    // 尝试从colorToAsin中获取更准确的颜色信息
    for (const [colorName, colorData] of Object.entries(colorToAsin)) {
      if (colorData.asin === asin) {
        variationAttributes.Color = colorName;
        displayName = colorName;
        break;
      }
    }

    // 获取变体图片
    if (variationAttributes.Color && colorImages[variationAttributes.Color]) {
      const images = colorImages[variationAttributes.Color];
      if (images && images.length > 0 && images[0].large) {
        skuData.imageUrl = images[0].large;
      }
    }

    skuData.variationAttributes = JSON.stringify(variationAttributes);

    console.log(`SKU ${asin}: ${displayName}`);
    skuDataList.push(skuData);
  }

  console.log(`提取到 ${skuDataList.length} 个SKU变体`);
  return skuDataList;
}

/**
 * 为单变体产品创建SKU数据
 * @param {Document} doc 页面文档对象
 * @param {string} parentAsin 父ASIN
 * @returns {Array} 包含单个SKU的数组
 */
function createSingleSkuFromPage(doc, parentAsin) {
  console.log("创建单变体SKU数据...");

  const skuData = {
    asin: parentAsin,
    parentAsin: parentAsin,
    currency: 'USD',
    stockStatus: 'In Stock',
    price: null,
    imageUrl: null,
    variationAttributes: JSON.stringify({})
  };

  // 提取价格
  const priceElement = doc.querySelector('.a-price .a-offscreen');
  if (priceElement) {
    const priceText = priceElement.textContent.trim();
    const priceMatch = priceText.replace('$', '').match(/[\d,]+\.?\d*/);
    if (priceMatch) {
      skuData.price = parseFloat(priceMatch[0].replace(',', ''));
      console.log("价格:", skuData.price);
    }
  }

  // 提取主图
  const mainImageElement = doc.querySelector('#landingImage, #imgBlkFront');
  if (mainImageElement) {
    skuData.imageUrl = mainImageElement.src || mainImageElement.getAttribute('data-src');
    console.log("主图:", skuData.imageUrl?.substring(0, 50) + "...");
  }

  // 提取库存状态
  const stockSelectors = [
    '#availability span',
    '#availabilityInsideBuyBox_feature_div span',
    '.a-color-success',
    '.a-color-state',
    '.a-color-price'
  ];

  for (const selector of stockSelectors) {
    const availabilityElement = doc.querySelector(selector);
    if (availabilityElement) {
      const availabilityText = availabilityElement.textContent.trim();
      if (availabilityText && availabilityText.length > 0) {
        if (availabilityText.includes('In Stock')) {
          skuData.stockStatus = 'In Stock';
        } else if (availabilityText.includes('Out of Stock')) {
          skuData.stockStatus = 'Out of Stock';
        } else if (availabilityText.toLowerCase().includes('only') &&
                   availabilityText.toLowerCase().includes('left')) {
          const quantityMatch = availabilityText.toLowerCase().match(/only\s+(\d+)\s+left/);
          if (quantityMatch) {
            skuData.stockStatus = `Only ${quantityMatch[1]} left in stock`;
          } else {
            skuData.stockStatus = availabilityText.substring(0, 50);
          }
        } else {
          skuData.stockStatus = availabilityText.substring(0, 50);
        }

        console.log("库存状态:", skuData.stockStatus);
        break;
      }
    }
  }

  console.log("单变体SKU创建完成");
  return [skuData];
}

/**
 * 获取每个SKU的详细信息（价格、库存等）
 * @param {Array} skuDataList SKU数据数组
 * @param {string} baseUrl 基础URL
 * @returns {Promise<Array>} 更新后的SKU数据数组
 */
async function fetchIndividualSkuDetails(skuDataList, baseUrl = "https://www.amazon.com") {
  console.log(`开始获取 ${skuDataList.length} 个SKU的详细信息...`);

  const updatedSkuList = [];

  for (let i = 0; i < skuDataList.length; i++) {
    const skuData = { ...skuDataList[i] };
    const asin = skuData.asin;
    const variationAttrs = JSON.parse(skuData.variationAttributes || '{}');
    const displayName = variationAttrs.Color || `Variant ${i + 1}`;

    console.log(`正在获取SKU ${i + 1}/${skuDataList.length}: ${asin} (${displayName})`);

    try {
      // 构建SKU详情页URL
      const skuUrl = `${baseUrl}/dp/${asin}`;

      // 发送请求到content script来获取SKU详情
      const skuDetails = await requestSkuDetailsFromContentScript(asin, skuUrl);

      if (skuDetails) {
        // 更新SKU数据
        if (skuDetails.price !== undefined) {
          skuData.price = skuDetails.price;
        }
        if (skuDetails.stockStatus) {
          skuData.stockStatus = skuDetails.stockStatus;
        }
        if (skuDetails.imageUrl) {
          skuData.imageUrl = skuDetails.imageUrl;
        }

        console.log(`SKU ${asin} 详情获取成功: 价格=$${skuData.price}, 库存=${skuData.stockStatus}`);
      } else {
        console.warn(`SKU ${asin} 详情获取失败，使用默认值`);
        // 使用模拟数据作为后备
        skuData.price = generateMockPrice(displayName);
        skuData.stockStatus = 'In Stock';
      }

      updatedSkuList.push(skuData);

      // 添加随机延迟，避免请求过于频繁
      if (i < skuDataList.length - 1) {
        const delay = Math.random() * 2000 + 1000; // 1-3秒随机延迟
        await new Promise(resolve => setTimeout(resolve, delay));
      }

    } catch (error) {
      console.error(`获取SKU ${asin} 详情时出错:`, error);

      // 使用模拟数据作为后备
      skuData.price = generateMockPrice(displayName);
      skuData.stockStatus = 'In Stock';
      updatedSkuList.push(skuData);
    }
  }

  console.log(`所有SKU详情获取完成，成功处理 ${updatedSkuList.length} 个SKU`);
  return updatedSkuList;
}

/**
 * 向content script请求SKU详情
 * @param {string} asin SKU的ASIN
 * @param {string} url SKU详情页URL
 * @returns {Promise<Object|null>} SKU详情数据
 */
async function requestSkuDetailsFromContentScript(asin, url) {
  return new Promise((resolve) => {
    // 查找活动的标签页
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (tabs.length === 0) {
        resolve(null);
        return;
      }

      const tabId = tabs[0].id;

      // 发送消息到content script
      chrome.tabs.sendMessage(tabId, {
        action: 'fetchSkuDetails',
        asin: asin,
        url: url
      }, (response) => {
        if (chrome.runtime.lastError) {
          console.warn(`请求SKU ${asin} 详情失败:`, chrome.runtime.lastError.message);
          resolve(null);
        } else {
          resolve(response);
        }
      });
    });
  });
}

/**
 * 生成模拟价格（作为后备方案）
 * @param {string} displayName 变体显示名称
 * @returns {number} 模拟价格
 */
function generateMockPrice(displayName) {
  const basePrice = 9.99;

  // 根据颜色生成不同的价格
  if (displayName.includes('Blue')) {
    return basePrice;
  } else if (displayName.includes('Light Pink')) {
    return basePrice + 1.00;
  } else if (displayName.includes('Peach Pink')) {
    return basePrice + 0.50;
  } else if (displayName.includes('Pink')) {
    return basePrice + 2.00;
  } else if (displayName.includes('Champagne')) {
    return basePrice + 1.50;
  } else if (displayName.includes('Gray')) {
    return basePrice + 0.75;
  } else if (displayName.includes('Green')) {
    return basePrice + 1.25;
  } else {
    // 随机价格变化
    const variation = (Math.random() - 0.5) * 4; // -2到+2的变化
    return Math.max(basePrice + variation, 5.99); // 最低价格5.99
  }
}

/**
 * 主要的Amazon产品详情采集流程
 * @param {string} productUrl 产品详情页URL
 * @param {number} taskId 任务ID
 * @param {string} clientId 客户端ID
 * @returns {Promise<boolean>} 是否成功
 */
async function processAmazonProductDetail(productUrl, taskId, clientId) {
  console.log(`开始处理Amazon产品详情: ${productUrl} (任务ID: ${taskId}, 客户端: ${clientId})`);

  try {
    // 1. 验证URL格式
    if (!productUrl || !productUrl.includes('amazon.com/dp/')) {
      throw new Error("无效的Amazon产品URL");
    }

    // 2. 获取页面内容（通过content script）
    const pageData = await requestPageDataFromContentScript(productUrl);
    if (!pageData || !pageData.html) {
      throw new Error("无法获取页面数据，请确保在Amazon页面运行");
    }

    // 3. 解析页面内容
    const parser = new DOMParser();
    const doc = parser.parseFromString(pageData.html, 'text/html');

    // 4. 提取SPU数据
    console.log("正在提取SPU数据...");
    const spuData = extractSpuData(doc, pageData.url);
    if (!spuData.asin || spuData.asin === 'UNKNOWN') {
      throw new Error("无法提取产品ASIN，可能页面结构不正确");
    }

    // 5. 提取变体数据
    console.log("正在提取变体数据...");
    const variationData = extractVariationData(doc);

    // 6. 判断是否为多变体产品并提取SKU数据
    let skuDataList = [];

    if (isMultiVariantProduct(variationData)) {
      console.log(`检测到多变体产品，父ASIN: ${spuData.asin}`);
      skuDataList = extractSkuDataFromVariations(variationData, spuData.asin);

      // 获取每个SKU的详细信息
      if (skuDataList.length > 0) {
        console.log(`开始获取 ${skuDataList.length} 个SKU的详细信息...`);
        skuDataList = await fetchIndividualSkuDetails(skuDataList);
      }
    } else {
      console.log(`检测到单变体产品，ASIN: ${spuData.asin}`);
      skuDataList = createSingleSkuFromPage(doc, spuData.asin);
    }

    // 7. 验证数据完整性
    if (skuDataList.length === 0) {
      console.warn("未提取到任何SKU数据，创建默认SKU");
      skuDataList = [{
        asin: spuData.asin,
        parentAsin: spuData.asin,
        currency: 'USD',
        stockStatus: 'Unknown',
        price: null,
        imageUrl: spuData.mainImageUrl,
        variationAttributes: JSON.stringify({})
      }];
    }

    // 8. 上传SPU数据
    console.log("正在上传SPU数据...");
    const spuSuccess = await submitSpuData(spuData);
    if (!spuSuccess) {
      throw new Error("SPU数据上传失败");
    }

    // 9. 批量上传SKU数据
    console.log(`正在上传 ${skuDataList.length} 个SKU数据...`);
    const skuSuccess = await submitSkuDataBatch(skuDataList);
    if (!skuSuccess) {
      throw new Error("SKU数据上传失败");
    }

    // 10. 标记任务完成
    if (taskId > 0) {
      await markDetailTaskCompleted(taskId, true);
    }

    console.log(`✅ 产品详情采集完成: ${spuData.asin}, SPU: 1个, SKU: ${skuDataList.length}个`);
    return true;

  } catch (error) {
    console.error("❌ 处理Amazon产品详情失败:", error);

    // 标记任务失败
    if (taskId > 0) {
      await markDetailTaskCompleted(taskId, false, error.message);
    }
    return false;
  }
}

/**
 * 向content script请求页面数据
 * @param {string} url 页面URL
 * @returns {Promise<Object|null>} 页面数据
 */
async function requestPageDataFromContentScript(url) {
  return new Promise((resolve) => {
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (tabs.length === 0) {
        resolve(null);
        return;
      }

      const tabId = tabs[0].id;

      chrome.tabs.sendMessage(tabId, {
        action: 'getPageData',
        url: url
      }, (response) => {
        if (chrome.runtime.lastError) {
          console.warn("请求页面数据失败:", chrome.runtime.lastError.message);
          resolve(null);
        } else {
          resolve(response);
        }
      });
    });
  });
}

/**
 * Amazon产品详情采集任务循环
 * @param {string} clientId 客户端ID
 */
async function startAmazonDetailCrawlingLoop(clientId) {
  console.log("启动Amazon产品详情采集任务循环...");

  while (true) {
    try {
      // 获取待处理的任务
      const task = await getPendingDetailTask(clientId);

      if (!task) {
        console.log("暂无待处理的详情采集任务，等待30秒后重试...");
        await new Promise(resolve => setTimeout(resolve, 30000));
        continue;
      }

      console.log(`获取到详情采集任务: ${task.id} - ${task.productUrl}`);

      // 处理任务
      const success = await processAmazonProductDetail(task.productUrl, task.id, clientId);

      if (success) {
        console.log(`任务 ${task.id} 处理成功`);
      } else {
        console.log(`任务 ${task.id} 处理失败`);
      }

      // 随机等待10-15秒后处理下一个任务
      const waitTime = Math.random() * 5000 + 10000; // 10-15秒
      console.log(`等待 ${Math.round(waitTime/1000)} 秒后处理下一个任务...`);
      await new Promise(resolve => setTimeout(resolve, waitTime));

    } catch (error) {
      console.error("任务循环出错:", error);

      // 出错后等待60秒再重试
      console.log("等待60秒后重试...");
      await new Promise(resolve => setTimeout(resolve, 60000));
    }
  }
}

// 监听来自内容脚本或其他扩展部分的连接请求
chrome.runtime.onConnect.addListener(function (port) {
  if (port.name === "popup-to-background") {
    port.onMessage.addListener(function (msg) {
      if (chrome.runtime.lastError) {
        console.error(chrome.runtime.lastError.message);
        return;
      }
      console.log(ct(), "后台接收消息， received:", msg);
      // 处理消息
    });
    port.onDisconnect.addListener(function () {
      console.log(ct(), "Disconnected from popup");
    });
  }
});

chrome.runtime.onInstalled.addListener(() => {
  console.log(ct(), "采集组件 onInstalled 我是后台js...");

  chrome.declarativeNetRequest.updateDynamicRules(
    {
      addRules: [
        {
          id: 31112,
          priority: 1,
          action: { type: "block" },
          condition: {
            urlFilter: "*://*.doubleclick.net/*",
            domains: ["allegro.pl"],
            resourceTypes: ["script"],
          },
        },
        {
          id: 31113,
          priority: 1,
          action: { type: "block" },
          condition: {
            urlFilter: "*://*.facebook.com/*",
            domains: ["allegro.pl"],
            resourceTypes: ["script"],
          },
        },
        {
          id: 31114,
          priority: 1,
          action: { type: "block" },
          condition: {
            urlFilter: "*://*.tiktok.com/*",
            domains: ["allegro.pl"],
            resourceTypes: ["script"],
          },
        },
        
      ],
    },
    function () {}
  );
});

xintiao(); // 立即执行一次心跳函数
setInterval(xintiao, 10000); // 每10秒重复执行一次心跳函数

chrome.runtime.onStartup.addListener(function () {
  console.log(ct(), "采集组件onStartup，我是后台js...");
  // xintiao(); // 立即执行一次心跳函数
  // setInterval(xintiao, 10000); // 每10秒重复执行一次心跳函数
});




function generateUniqueId() {
  var chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
  var result = "";
  for (var i = 0; i < 5; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

function erpErrLog(msg) {
  console.error(ct() + msg);
}

function ct() {
  var now = new Date();
  var year = now.getFullYear();
  var month = now.getMonth() + 1; // 月份是从0开始的
  var day = now.getDate();
  var hours = now.getHours();
  var minutes = now.getMinutes();
  var seconds = now.getSeconds();

  // 补零函数
  function pad(number) {
    return (number < 10 ? "0" : "") + number;
  }

  // 返回自定义格式的日期时间字符串
  return (
    year +
    "-" +
    pad(month) +
    "-" +
    pad(day) +
    " " +
    pad(hours) +
    ":" +
    pad(minutes) +
    ":" +
    pad(seconds) +
    " "
  );
}
