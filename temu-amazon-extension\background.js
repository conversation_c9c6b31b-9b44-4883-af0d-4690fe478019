// 导入配置文件
importScripts('config.js');

// 获取配置
const backgroundConfig = AmazonCrawlerConfig || {};

// 基础配置
//var BASE_URL = "https://51erp.store/xace-web/api/amazon";
var BASE_URL = "http://127.0.0.1:32000/api/amazon";

// 使用配置文件中的API端点，如果配置不存在则使用默认值
var WAITGET_URL = BASE_URL + "/list/task2/waitGets";
var POSTOFFER_URL = BASE_URL + "/collect2/putOfferJson";
var BATCH_URL = BASE_URL + "/collect/batch";
var NEW_BATCH_URL = BASE_URL + "/collect2/newSellerCollect";
var REMOVE_URL = BASE_URL + "/collect/remove";
var CATE_WAITGET_URL = BASE_URL + "/task/waitGets";
var CATE_POSTOFFER_URL = BASE_URL + "/task/putTaskJson";

// Amazon产品详情采集相关API
var DETAIL_TASK_WAITGET_URL = BASE_URL + "/page/task/waitGets";
var DETAIL_TASK_SUBMIT_URL = BASE_URL + "/page/task/submitResult";
var PRODUCT_DETAIL_SUBMIT_URL = BASE_URL + "/page/task/submitSpuSku";

var uniqueId = "";
let lastUpdateTime = Date.now();


function util_getscript_sellerData_content(html = "") {
  var scriptTagPattern = /<script\b[^>]*>([\s\S]*?)<\/script>/gm;
  var matches = html.match(scriptTagPattern);
  if (matches) {
    for (var i = 0; i < matches.length; i++) {
      var scriptContent = matches[i].replace(scriptTagPattern, "$1");
      if (
        scriptContent.indexOf("forceBuyNow") != -1 &&
        scriptContent.indexOf("offer") != -1 &&
        scriptContent.indexOf("seller") != -1 &&
        scriptContent.indexOf("company") != -1
      ) {
        return scriptContent;
      }
    }
  }
  return false;
}

function util_get_random_number(min = 1000, max = 5000) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

function util_getscript_genmaiData_content(html = "") {
  var scriptTagPattern = /<script\b[^>]*>([\s\S]*?)<\/script>/gm;
  var matches = html.match(scriptTagPattern);
  if (matches) {
    for (var i = 0; i < matches.length; i++) {
      var scriptContent = matches[i].replace(scriptTagPattern, "$1");
      if (
        scriptContent.indexOf("ata-analytics-view-custom-cheapest-price") != -1
      ) {
        return scriptContent;
      }
    }
  }
  return false;
}

function util_checkFanpa(str = "") {
  const searchString = "This item is sold out";
  const regex = new RegExp(searchString);
  return regex.test(str);
}

function pause_s_time() {
  chrome.storage.local.set({ pause_s_time: Date.now() });
}

// 获取 HTML 中指定选择器对应元素的文本内容
function extractTextFromHTML(html, selector) {
  const regex = new RegExp(`<${selector}[^>]*>([^<]*)<\/${selector}>`, "g");
  const matches = [];
  let match;
  while ((match = regex.exec(html)) !== null) {
    matches.push(match[1]);
  }
  return matches.length > 0 ? matches.join(" ") : "";
}

async function request_offer() {
  console.log(ct(), "后台请求offer详情");

  chrome.storage.local.get(["uniqueId"], function (result) {
    if (!result.hasOwnProperty("uniqueId")) {
      uniqueId = generateUniqueId();
      chrome.storage.local.set({ uniqueId: uniqueId }, function () {
        console.log(ct(), "Unique ID is set to " + uniqueId);
      });
    } else {
      uniqueId = result.uniqueId;
    }

    // 请求接口获取数据
    fetch(WAITGET_URL, {
      method: "GET",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
      },
    })
      .then((response) => response.json())
      .then((data) => {
        if (data.code === 200) {
          console.log(ct(), "请求到：waitGets数据。" + data.data.length);
       
          if (data.data.length == 0) {
            //等待1个小时
            console.log(
              ct(),
              "===============后台没有待处理数据，等待15分钟=================="
            );
            setTimeout(pause_s_time, 900000); // 15分钟 = 15 * 60 * 1000 毫秒
          }

          isRequest = true;

          data.data.forEach((item) => {
            console.log(
              ct(),
              ".....加载：" + item.offerLink + ", ID:" + item.id
            );
            let key = "request_offer_detail_" + item.id;
            let obj = {
              [key]: { link: item.offerLink, time: new Date().getTime() },
            };
            chrome.storage.local.set(obj);

            const requestOptions = {
              method: 'GET',
              headers: {
                'accept': 'application/json, text/javascript, */*; q=0.01',
                'Content-Type': 'application/json'
              },  
            };     

            fetch(item.offerLink,requestOptions)
              .then((response) => {
                console.log(ct(), "响应状态码：" + response.status); // 打印状态码
                if (response.status === 403) {
                  // 如果状态码是403，等待
                  pause_s_time();
                }
                if (response.status === 404) {
                  let offerRemove = {
                    cpId: item.id,
                    clientId: uniqueId,
                    reason:  "404错误"
                  };

                  // 如果状态码是404，发送cpId到REMOVE_URL并停止处理
                  console.log(
                    ct(),
                    `链接 ${item.offerLink} 返回404，将cpId${item.id} 报送给REMOVE_URL`
                  );
                  fetch(REMOVE_URL, {
                    method: "PUT",
                    headers: {
                      Accept: "application/json",
                      "Content-Type": "application/json",
                    },
                    body: JSON.stringify(offerRemove),
                  })
                    .then((removeResponse) => removeResponse.json())
                    .then((removeData) => {
                      console.log(ct(), "REMOVE_URL 响应:", removeData);
                      // 这里可以添加额外的逻辑来处理REMOVE_URL的响应
                    })
                    .catch((removeError) => {
                      erpErrLog("发送到REMOVE_URL时出错:", removeError);
                    });
                  // 不再继续执行下面的代码，因为链接是404
                  return;
                }
                // 如果状态码不是404，继续处理响应体
                return response.json();
              })
              .then((data) => {
                // console.log(ct(), "数据抓取成功:", data);



                if (!data) {
                  // 如果data是undefined，不执行任何操作或进行错误处理
                  return;
                }
                var productHeader = {};
                if (data["allegro.showoffer.productHeader"] != undefined)
                    productHeader = data["allegro.showoffer.productHeader"];
                else
                    productHeader = data["showoffer.productHeader"];



                if(productHeader["offer"]["view"]["type"]==("ENDED")){
                  // 如果碰到销售结束，则删除数据
                  let offerRemove = {
                    cpId: item.id,
                    clientId: uniqueId,
                    reason:  "销售结束"
                  };

                  // 如果状态码是404，发送cpId到REMOVE_URL并停止处理
                  console.log(
                    ct(),
                    `链接 ${item.offerLink} 销售结束，将cpId${item.id} 报送给REMOVE_URL`
                  );
                  fetch(REMOVE_URL, {
                    method: "PUT",
                    headers: {
                      Accept: "application/json",
                      "Content-Type": "application/json",
                    },
                    body: JSON.stringify(offerRemove),
                  })
                    .then((removeResponse) => removeResponse.json())
                    .then((removeData) => {
                      console.log(ct(), "REMOVE_URL 响应:", removeData);
                      // 这里可以添加额外的逻辑来处理REMOVE_URL的响应
                    })
                    .catch((removeError) => {
                      erpErrLog("发送到REMOVE_URL时出错:", removeError);
                    });
                  // 不再继续执行下面的代码，因为链接是404
                  return;

                }



             
                  if (productHeader) {

                    let offer = {
                      cpId: item.id,
                      offerJson: productHeader,
                      customJson:{},
                      clientId: uniqueId,
                    };

                    fetch(POSTOFFER_URL, {
                      method: "PUT",
                      headers: {
                        Accept: "application/json",
                        "Content-Type": "application/json",
                      },
                      body: JSON.stringify(offer),
                    })
                      .then((response) => response.json())
                      .then((data) => {
                        console.log(ct(), "数据保存成功:", data.msg);
                      })
                      .catch((error) => {
                        erpErrLog("数据保存失败:", error);
                      });
                  } else {
                    pause_s_time();
                    sendPopTips(
                      "遇到反爬,请开启或者更换代理或者暂停采集，稍后再采集!"
                    );
                  }
                
                chrome.storage.local.remove(key, function () {
                  console.log(ct(), `键名 ${key} 的值已被清空！`);
                });
              })
              .catch((error) => {
                chrome.storage.local.remove(key, function () {
                  erpErrLog(`数据抓取错误 ，键名 ${key} 的值已被清空！`, error);
                });
                pause_s_time();
                sendPopTips(
                  "遇到反爬,请验证,请开启或者更换代理或者暂停采集，稍后再采集!"
                );
              });
          });
        } else {
          erpErrLog("51ERP API请求失败:", data.msg);
        }
      })
      .catch((error) => {
        erpErrLog("请求错误:", error);
      });
  });
}

function request_shangjia_company_page_link() {
  chrome.storage.local.get(null, function (items) {
    let need_item = null;
    for (let key in items) {
      if (key.indexOf("shangjia_about_contact_and_basic_info_") != -1) {
        if (
          items[key]["_is_request"] == 2 &&
          items[key]["_seller_page_url"] &&
          items[key]["_seller_page_url_is_request"] == 0
        ) {
          need_item = items[key];
          need_item["_seller_page_url_is_request"] = 1;
          need_item["_seller_page_url_s_time"] = Date.now();
          let obj = {};
          obj[key] = need_item;
          chrome.storage.local.set(obj, function () {
            request_shangjia_company_page_link_ajax(key);
          });
          break;
        }
      }
    }
  });
}

function xintiao() {
  chrome.storage.local.get(null, function (items) {
    if (!items.hasOwnProperty("pause") || !items["pause"]) {
      if (
        items.hasOwnProperty("pause_s_time") &&
        items["pause_s_time"] + 600000 > Date.now()
      ) {
        console.log(ct(), "后台详情爬取失败，暂停10分钟");
      } else {
        console.log(ct(), "后台详情爬取中:");
        request_shangjia_about_contact_and_basic_info();
      }
    } else {
      console.log(ct(), "=========后台详情已暂停爬取================");
    }
  });

  // 20240731 新增
  if (Date.now() - lastUpdateTime > 3 * 60 * 1000) { // 3分钟 content.js 没动作，需要刷新一下前端活动页面。
    // chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
    //     chrome.tabs.reload(tabs[0].id);
    // });

     chrome.tabs.query({currentWindow: true}, function(tabs) {
            tabs.forEach(function(tab) {
                // 可以在这里添加条件判断，只刷新特定标签页
                // 例如，只刷新包含特定网址的标签页
                 if (tab.url && tab.url.includes('amazon.com')) {
                    chrome.tabs.reload(tab.id);
                 }
            });
        });
        lastUpdateTime = Date.now() ; // 重置时间更新
    
    

}
}

function request_shangjia_about_contact_and_basic_info() {
  chrome.storage.local.get(null, function (items) {
    let binfa = 0;
    for (let key in items) {
      if (key.indexOf("request_offer_detail_") != -1) {
        if (items[key]["time"] + 300000 > Date.now()) {
          chrome.storage.local.remove(key, function () {
            console.log(ct(), `键名 ${key} 的值已被清空,5分钟未处理的数据！`);
          });
        }
        binfa++;
        if (binfa == 20) {
          break;
        }
      }
    }
    if (binfa < 20) {
      console.log(ct(), "当前并发数：" + binfa);
      request_offer();
    }
  });
}

function storage_shangjias_update(taskType,shangjia_infos = []) {
  chrome.storage.local.get(null, function (items) {
    let total_add_shangjia_num = items.hasOwnProperty("total_add_shangjia_num")
      ? items["total_add_shangjia_num"]
      : 0;
    total_add_shangjia_num += shangjia_infos.length;
    chrome.storage.local.set({
      total_add_shangjia_num: total_add_shangjia_num,
    });

    sendPutRequest(taskType,shangjia_infos)
      .then((responseData) => {
        console.log(ct(), "成功提交到后台:", responseData);
      })
      .catch((error) => {
        erpErrLog("Fetch error details:", error);
        erpErrLog("Error stack trace:", error.stack);
      });
  });
}

function sendPutRequest(taskType,data, retries = 3) {
  const headers = {
    "Content-Type": "application/json",
  };
  return new Promise((resolve, reject) => {
    function attempt(remainingRetries) {
      fetch(NEW_BATCH_URL+"/"+taskType, {
        method: "PUT",
        headers: headers,
        body: JSON.stringify(data),
      })
        .then((response) => response.json())
        .then(resolve)
        .catch((error) => {
          erpErrLog(`Attempt failed: ${error}`);
          if (remainingRetries > 0) {
            erpErrLog(
              `重试中... (${retries - remainingRetries + 1}/${retries})`
            );
            setTimeout(() => attempt(remainingRetries - 1), 20000);
            sendPopTips(
              "网络错误，请检查网络连接,数据提交不成功，请确认是否使用代理导致的"
            );
          } else {
            reject(new Error(`HTTP error! status: ${error}`));
          }
        });
    }
    attempt(retries);
  });
}

function storage_get(callback) {
  chrome.storage.local.get(null, callback);
}

chrome.runtime.onMessage.addListener(function (request, sender, sendResponse) {
  if (request.action === "updateTime") {
    lastUpdateTime = Date.now();
    sendResponse({state: "time updated："+new Date(lastUpdateTime).toLocaleString()});
}else  if (request.action == "shangjia_items_update") {
    console.log("action", request, sender, sendResponse);
    let shangjia_infos = request.shangjia_infos;
    storage_shangjias_update(request.taskType, shangjia_infos);
    sendResponse({ state: "商品保存成功！" });
  } else if (request.action == "can_zidong_caiji") {
    chrome.storage.local.get(null, function (items) {
      if (!items.hasOwnProperty("pause_page") || items["pause_page"] == 0) {
        let total_add_shangjia_num = items.hasOwnProperty(
          "total_add_shangjia_num"
        )
          ? items["total_add_shangjia_num"]
          : 0;

        uniqueId = items.hasOwnProperty("uniqueId") ? items["uniqueId"] : "";
        console.log(ct(), "类目采集： uniqueId:" + uniqueId);

        var collect_item_num = items["collect_item_num"] || 10000;
        let msg = {
          state: 0,
          collect_item_num: collect_item_num,
          collect_zero_sales: items["collect_zero_sales"] || false,
          collect_min_price: items["collect_min_price"] || 50,
          collect_max_price: items["collect_max_price"] || 500,
          collect_max_page: items["collect_max_page"] || 100,
          uniqueId: uniqueId,
        };
        sendResponse(JSON.stringify(msg));
        

        // if (total_add_shangjia_num <= collect_item_num) {
          
        // } else {
        //   sendPopTips("商品达到上限，停止收集！");
        //   sendResponse(JSON.stringify({ state: 1 }));
        // }
      } else {
        sendResponse(JSON.stringify({ state: 1 }));
      }
    });
    return true;
  } else if (request.action == "start_detail_crawling") {
    // 启动Amazon产品详情采集
    chrome.storage.local.get(["uniqueId"], function (items) {
      const uniqueId = items.uniqueId || (backgroundConfig.generateUniqueId ? backgroundConfig.generateUniqueId() : generateUniqueId());
      chrome.storage.local.set({ uniqueId: uniqueId });

      // 启动采集循环（在后台运行）
      startAmazonDetailCrawlingLoop(uniqueId);

      sendResponse({ success: true, message: "Amazon产品详情采集已启动" });
    });
    return true;
  } else if (request.action == "process_single_product") {
    // 处理单个产品详情
    chrome.storage.local.get(["uniqueId"], async function (items) {
      const uniqueId = items.uniqueId || (backgroundConfig.generateUniqueId ? backgroundConfig.generateUniqueId() : generateUniqueId());

      try {
        const success = await processAmazonProductDetail(
          request.productUrl,
          request.taskId || 0,
          uniqueId
        );
        sendResponse({ success: success });
      } catch (error) {
        sendResponse({ success: false, error: error.message });
      }
    });
    return true;
  } else if (request.action == "stop_detail_crawling") {
    // 停止详情采集循环
    stopAmazonDetailCrawlingLoop();
    sendResponse({ success: true, message: "采集循环已停止" });
    return true;
  } else if (request.action == "get_crawling_stats") {
    // 获取采集统计信息
    sendResponse({ success: true, stats: getCrawlingStats() });
    return true;
  }
});

// 全局变量跟踪采集进度
let detailCrawlingStats = {
  isRunning: false,
  totalProcessed: 0,
  currentProduct: '',
  currentProductTitle: '',
  startTime: null,
  lastUpdateTime: null
};

// 这个函数用于发送消息到弹窗
function sendPopTips(msg = "", url = "") {
  // 寻找一个连接是来自弹窗的端口
  // let popupPort = null;
  // chrome.runtime_ports.forEach((port) => {
  //     if (port.name === "popup") {
  //         popupPort = port;
  //     }
  // });
  // if (popupPort) {
  //     popupPort.postMessage({ action: "background-tips", info: msg, url: url });
  // } else {
  //     console.error("No popup port found");
  //     // 可能需要打开弹窗或创建连接
  // }
}

/**
 * 更新采集进度并通知popup
 * @param {string} currentProduct 当前处理的产品ASIN
 * @param {string} currentProductTitle 当前处理的产品标题
 * @param {boolean} isCompleted 是否完成
 */
function updateCrawlingProgress(currentProduct = '', currentProductTitle = '', isCompleted = false) {
  if (isCompleted) {
    detailCrawlingStats.totalProcessed++;
  }

  detailCrawlingStats.currentProduct = currentProduct;
  detailCrawlingStats.currentProductTitle = currentProductTitle;
  detailCrawlingStats.lastUpdateTime = new Date().toISOString();

  // 保存到本地存储
  chrome.storage.local.set({ detailCrawlingStats: detailCrawlingStats });

  // 发送消息到popup（如果打开的话）
  try {
    chrome.runtime.sendMessage({
      action: 'detail_crawling_progress',
      stats: detailCrawlingStats
    });
  } catch (error) {
    // popup可能没有打开，忽略错误
  }

  console.log(`采集进度更新: 已处理${detailCrawlingStats.totalProcessed}个产品, 当前: ${currentProductTitle || currentProduct}`);
}

/**
 * 获取当前采集统计信息
 * @returns {Object} 采集统计信息
 */
function getCrawlingStats() {
  return { ...detailCrawlingStats };
}

// ==================== Amazon产品详情采集系统 ====================

/**
 * 获取待处理的Amazon产品详情采集任务
 * @param {string} clientId 客户端ID
 * @returns {Promise<Object|null>} 任务对象或null
 */
async function getPendingDetailTask(clientId) {
  try {
    const url = `${DETAIL_TASK_WAITGET_URL}?clientId=${clientId}`;
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`获取详情采集任务失败，状态码: ${response.status}`);
    }

    const data = await response.json();
    console.log("获取到详情采集任务:", data);

    if (data.success && data.data) {
      // 适配新的响应格式，将url字段映射为productUrl
      const task = {
        ...data.data,
        productUrl: data.data.url // 后台返回url字段，前端使用productUrl
      };
      return task;
    }

    return null;
  } catch (error) {
    console.error("获取详情采集任务失败:", error);
    return null;
  }
}

/**
 * 一次性提交完整的产品详情数据（SPU + SKU）
 * @param {Object} spuData SPU数据对象
 * @param {Array} skuDataList SKU数据数组
 * @returns {Promise<boolean>} 是否成功
 */
async function submitProductDetailData(spuData, skuDataList) {
  try {
    const payload = {
      spu: spuData,
      skuList: skuDataList,
      timestamp: new Date().toISOString(),
      totalSkuCount: skuDataList.length
    };

    console.log("提交产品详情数据:", {
      spuAsin: spuData.asin,
      skuCount: skuDataList.length,
      payload: payload
    });

    const response = await fetch(PRODUCT_DETAIL_SUBMIT_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      throw new Error(`提交产品详情数据失败，状态码: ${response.status}`);
    }

    const result = await response.json();
    console.log(`产品详情数据提交响应:`, result);

    if (result.success) {
      console.log(`✅ 产品详情数据提交成功: SPU=${spuData.asin}, SKU数量=${skuDataList.length}`);
      if (result.data) {
        console.log(`处理结果: spuId=${result.data.spuId}, skuCount=${result.data.skuCount}, processedAt=${result.data.processedAt}`);
      }
      return true;
    } else {
      console.error("产品详情数据提交失败:", result.message || result.data);
      return false;
    }
  } catch (error) {
    console.error("提交产品详情数据失败:", error);
    return false;
  }
}

/**
 * 标记详情采集任务完成
 * @param {number} taskId 任务ID
 * @param {boolean} success 是否成功
 * @param {string} errorMessage 错误信息（如果失败）
 * @returns {Promise<boolean>} 是否成功
 */
async function markDetailTaskCompleted(taskId, success = true, errorMessage = '') {
  try {
    const payload = {
      taskId: taskId,
      status: success ? 'completed' : 'failed',
      errorMessage: errorMessage || '',
      completedAt: new Date().toISOString()
    };

    console.log("提交任务结果:", payload);

    const response = await fetch(DETAIL_TASK_SUBMIT_URL, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      throw new Error(`标记任务完成失败，状态码: ${response.status}`);
    }

    const result = await response.json();
    console.log("任务状态更新成功:", result);

    if (result.success) {
      return true;
    } else {
      console.error("任务状态更新失败:", result.message || result.data);
      return false;
    }
  } catch (error) {
    console.error("标记任务完成失败:", error);
    return false;
  }
}

// Amazon解析函数已移至amazon-parser.js模块

// 以下Amazon解析函数已移至amazon-parser.js模块：
// - extractVariationData()
// - isMultiVariantProduct()
// - extractSkuDataFromVariations()
// - createSingleSkuFromPage()
// - fetchIndividualSkuDetails()
// - requestSkuDetailsFromContentScript()
// - generateMockPrice()

// extractSkuDataFromVariations函数已移至amazon-parser.js

// createSingleSkuFromPage函数已移至amazon-parser.js

// fetchIndividualSkuDetails函数已移至amazon-parser.js

// requestSkuDetailsFromContentScript和generateMockPrice函数已移至amazon-parser.js

/**
 * 主要的Amazon产品详情采集流程
 * @param {string} productUrl 产品详情页URL
 * @param {number} taskId 任务ID
 * @param {string} clientId 客户端ID
 * @returns {Promise<boolean>} 是否成功
 */
async function processAmazonProductDetail(productUrl, taskId, clientId, taskData = null) {
  console.log(`开始处理Amazon产品详情: ${productUrl} (任务ID: ${taskId}, 客户端: ${clientId})`);

  try {
    // 1. 验证URL格式
    if (!productUrl || !productUrl.includes('amazon.com/dp/')) {
      throw new Error("无效的Amazon产品URL");
    }

    // 2. 更新进度 - 开始处理
    const asinMatch = productUrl.match(/\/dp\/([A-Z0-9]{10})/);
    const currentAsin = taskData?.entryAsin || (asinMatch ? asinMatch[1] : 'UNKNOWN');
    updateCrawlingProgress(currentAsin, '正在获取页面数据...', false);

    // 3. 获取页面内容（通过content script）
    const pageData = await requestPageDataFromContentScript(productUrl);
    if (!pageData || !pageData.html) {
      throw new Error("无法获取页面数据，请确保在Amazon页面运行");
    }

    // 4. 解析页面内容
    const parser = new DOMParser();
    const doc = parser.parseFromString(pageData.html, 'text/html');

    // 5. 提取SPU数据（使用amazon-parser.js中的函数）
    console.log("正在提取SPU数据...");
    updateCrawlingProgress(currentAsin, '正在提取产品基础信息...', false);
    const spuData = AmazonParser.extractSpuData(doc, pageData.url, taskData);
    if (!spuData.asin || spuData.asin === 'UNKNOWN') {
      throw new Error("无法提取产品ASIN，可能页面结构不正确");
    }

    // 6. 更新进度 - 显示产品标题
    updateCrawlingProgress(spuData.asin, spuData.title || taskData?.listPageTitle || '未知产品', false);

    // 7. 提取变体数据（使用amazon-parser.js中的函数）
    console.log("正在提取变体数据...");
    updateCrawlingProgress(spuData.asin, spuData.title + ' - 检测变体信息', false);
    const variationData = AmazonParser.extractVariationData(doc);

    // 8. 判断是否为多变体产品并提取SKU数据（使用amazon-parser.js中的函数）
    let skuDataList = [];

    if (AmazonParser.isMultiVariantProduct(variationData)) {
      console.log(`检测到多变体产品，父ASIN: ${spuData.asin}`);
      updateCrawlingProgress(spuData.asin, spuData.title + ' - 处理多变体产品', false);
      skuDataList = AmazonParser.extractSkuDataFromVariations(variationData, spuData.asin);

      // 获取每个SKU的详细信息（暂时跳过，因为需要重构）
      // if (skuDataList.length > 0) {
      //   console.log(`开始获取 ${skuDataList.length} 个SKU的详细信息...`);
      //   updateCrawlingProgress(spuData.asin, spuData.title + ` - 获取${skuDataList.length}个变体详情`, false);
      //   skuDataList = await AmazonParser.fetchIndividualSkuDetails(skuDataList);
      // }
    } else {
      console.log(`检测到单变体产品，ASIN: ${spuData.asin}`);
      updateCrawlingProgress(spuData.asin, spuData.title + ' - 处理单变体产品', false);
      skuDataList = AmazonParser.createSingleSkuFromPage(doc, spuData.asin);
    }

    // 7. 验证数据完整性
    if (skuDataList.length === 0) {
      console.warn("未提取到任何SKU数据，创建默认SKU");
      skuDataList = [{
        asin: spuData.asin,
        parentAsin: spuData.asin,
        currency: 'USD',
        stockStatus: 'Unknown',
        price: null,
        imageUrl: spuData.mainImageUrl,
        variationAttributes: JSON.stringify({})
      }];
    }

    // 9. 一次性上传SPU和SKU数据
    console.log(`正在上传产品详情数据: SPU=${spuData.asin}, SKU数量=${skuDataList.length}...`);
    updateCrawlingProgress(spuData.asin, spuData.title + ' - 上传数据到后台', false);
    const submitSuccess = await submitProductDetailData(spuData, skuDataList);
    if (!submitSuccess) {
      throw new Error("产品详情数据上传失败");
    }

    // 10. 标记任务完成
    if (taskId > 0) {
      await markDetailTaskCompleted(taskId, true);
    }

    // 11. 更新进度 - 完成
    updateCrawlingProgress(spuData.asin, spuData.title + ' - 采集完成', true);

    console.log(`✅ 产品详情采集完成: ${spuData.asin}, SPU: 1个, SKU: ${skuDataList.length}个`);
    return true;

  } catch (error) {
    console.error("❌ 处理Amazon产品详情失败:", error);

    // 标记任务失败
    if (taskId > 0) {
      await markDetailTaskCompleted(taskId, false, error.message);
    }
    return false;
  }
}

/**
 * 向content script请求页面数据
 * @param {string} url 页面URL
 * @returns {Promise<Object|null>} 页面数据
 */
async function requestPageDataFromContentScript(url) {
  return new Promise((resolve) => {
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (tabs.length === 0) {
        resolve(null);
        return;
      }

      const tabId = tabs[0].id;

      chrome.tabs.sendMessage(tabId, {
        action: 'getPageData',
        url: url
      }, (response) => {
        if (chrome.runtime.lastError) {
          console.warn("请求页面数据失败:", chrome.runtime.lastError.message);
          resolve(null);
        } else {
          resolve(response);
        }
      });
    });
  });
}

/**
 * Amazon产品详情采集任务循环
 * @param {string} clientId 客户端ID
 */
async function startAmazonDetailCrawlingLoop(clientId) {
  console.log("启动Amazon产品详情采集任务循环...");

  // 初始化采集状态
  detailCrawlingStats.isRunning = true;
  detailCrawlingStats.startTime = new Date().toISOString();
  detailCrawlingStats.totalProcessed = 0;
  updateCrawlingProgress('', '正在启动采集循环...', false);

  while (detailCrawlingStats.isRunning) {
    try {
      // 获取待处理的任务
      updateCrawlingProgress('', '正在获取待处理任务...', false);
      const task = await getPendingDetailTask(clientId);

      if (!task) {
        console.log("暂无待处理的详情采集任务，等待30秒后重试...");
        updateCrawlingProgress('', '暂无任务，等待中...', false);
        await new Promise(resolve => setTimeout(resolve, 30000));
        continue;
      }

      console.log(`获取到详情采集任务: ${task.id} - ${task.productUrl || task.url}`);
      console.log(`任务详情: entryAsin=${task.entryAsin}, listPageTitle=${task.listPageTitle}, status=${task.status}`);

      // 处理任务
      const success = await processAmazonProductDetail(task.productUrl || task.url, task.id, clientId, task);

      if (success) {
        console.log(`任务 ${task.id} 处理成功`);
      } else {
        console.log(`任务 ${task.id} 处理失败`);
      }

      // 随机等待10-15秒后处理下一个任务
      const waitTime = Math.random() * 5000 + 10000; // 10-15秒
      console.log(`等待 ${Math.round(waitTime/1000)} 秒后处理下一个任务...`);
      updateCrawlingProgress('', `等待${Math.round(waitTime/1000)}秒后继续...`, false);
      await new Promise(resolve => setTimeout(resolve, waitTime));

    } catch (error) {
      console.error("任务循环出错:", error);
      updateCrawlingProgress('', '出现错误，等待重试...', false);

      // 出错后等待60秒再重试
      console.log("等待60秒后重试...");
      await new Promise(resolve => setTimeout(resolve, 60000));
    }
  }

  console.log("Amazon产品详情采集任务循环已停止");
  updateCrawlingProgress('', '采集循环已停止', false);
}

/**
 * 停止Amazon产品详情采集循环
 */
function stopAmazonDetailCrawlingLoop() {
  console.log("正在停止Amazon产品详情采集循环...");
  detailCrawlingStats.isRunning = false;
  updateCrawlingProgress('', '正在停止采集循环...', false);
}

// ==================== 自动启动功能 ====================

/**
 * 检查是否应该自动启动详情采集
 */
async function checkAutoStartDetailCrawling() {
  try {
    const items = await chrome.storage.local.get(['auto_start_detail_crawling', 'uniqueId']);

    if (items.auto_start_detail_crawling === true && !detailCrawlingStats.isRunning) {
      const uniqueId = items.uniqueId || generateUniqueId();
      console.log("检测到自动启动设置，开始启动详情采集循环...");

      // 延迟5秒后启动，确保系统完全初始化
      setTimeout(() => {
        startAmazonDetailCrawlingLoop(uniqueId);
      }, 5000);
    }
  } catch (error) {
    console.error("检查自动启动设置失败:", error);
  }
}

// 扩展启动时检查自动启动设置
chrome.runtime.onStartup.addListener(() => {
  console.log("Chrome扩展启动，检查自动启动设置...");
  checkAutoStartDetailCrawling();
});

// 扩展安装或更新时检查自动启动设置
chrome.runtime.onInstalled.addListener(() => {
  console.log("Chrome扩展安装/更新，检查自动启动设置...");
  checkAutoStartDetailCrawling();
});

// Service Worker启动时检查自动启动设置
if (typeof chrome !== 'undefined' && chrome.runtime) {
  console.log("Service Worker启动，检查自动启动设置...");
  checkAutoStartDetailCrawling();
}

// 监听来自内容脚本或其他扩展部分的连接请求
chrome.runtime.onConnect.addListener(function (port) {
  if (port.name === "popup-to-background") {
    port.onMessage.addListener(function (msg) {
      if (chrome.runtime.lastError) {
        console.error(chrome.runtime.lastError.message);
        return;
      }
      console.log(ct(), "后台接收消息， received:", msg);
      // 处理消息
    });
    port.onDisconnect.addListener(function () {
      console.log(ct(), "Disconnected from popup");
    });
  }
});

chrome.runtime.onInstalled.addListener(() => {
  console.log(ct(), "采集组件 onInstalled 我是后台js...");

  chrome.declarativeNetRequest.updateDynamicRules(
    {
      addRules: [
        {
          id: 31112,
          priority: 1,
          action: { type: "block" },
          condition: {
            urlFilter: "*://*.doubleclick.net/*",
            domains: ["allegro.pl"],
            resourceTypes: ["script"],
          },
        },
        {
          id: 31113,
          priority: 1,
          action: { type: "block" },
          condition: {
            urlFilter: "*://*.facebook.com/*",
            domains: ["allegro.pl"],
            resourceTypes: ["script"],
          },
        },
        {
          id: 31114,
          priority: 1,
          action: { type: "block" },
          condition: {
            urlFilter: "*://*.tiktok.com/*",
            domains: ["allegro.pl"],
            resourceTypes: ["script"],
          },
        },
        
      ],
    },
    function () {}
  );
});

xintiao(); // 立即执行一次心跳函数
setInterval(xintiao, 10000); // 每10秒重复执行一次心跳函数

chrome.runtime.onStartup.addListener(function () {
  console.log(ct(), "采集组件onStartup，我是后台js...");
  // xintiao(); // 立即执行一次心跳函数
  // setInterval(xintiao, 10000); // 每10秒重复执行一次心跳函数
});




// 后备的generateUniqueId函数（如果config.js未加载）
function generateUniqueId() {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 15);
  return `client_${timestamp}_${random}_v1.0.0`;
}

function erpErrLog(msg) {
  console.error(ct() + msg);
}

function ct() {
  var now = new Date();
  var year = now.getFullYear();
  var month = now.getMonth() + 1; // 月份是从0开始的
  var day = now.getDate();
  var hours = now.getHours();
  var minutes = now.getMinutes();
  var seconds = now.getSeconds();

  // 补零函数
  function pad(number) {
    return (number < 10 ? "0" : "") + number;
  }

  // 返回自定义格式的日期时间字符串
  return (
    year +
    "-" +
    pad(month) +
    "-" +
    pad(day) +
    " " +
    pad(hours) +
    ":" +
    pad(minutes) +
    ":" +
    pad(seconds) +
    " "
  );
}
