// 使用配置文件中的API端点
const config = window.AmazonCrawlerConfig || {};
const { API_ENDPOINTS, AMAZON_CONFIG, isAmazonProductPage, extractAsinFromUrl } = config;

var BASE_URL = "https://51erp.store/xace-web/api/amazon";
// var BASE_URL = "http://51erp.store:30001/api/amazon";
//var BASE_URL = "http://127.0.0.1:32000/api/amazon";
//   var BASE_URL = "http://127.0.0.1:32000/api/amazon";

var LIST_TASK_WAITGET_URL = API_ENDPOINTS?.LIST_TASK_WAITGET || BASE_URL + "/list/task/waitGets";
var LIST_TASK_SUBMIT_URL = API_ENDPOINTS?.LIST_TASK_SUBMIT || BASE_URL + "/list/task/submitForm";
var client_version = chrome.runtime.getManifest().version;

// 从服务器获取待处理的Amazon列表采集任务
async function getPendingListTasks(uniqueId) {
  try {
    var url = LIST_TASK_WAITGET_URL + "?clientId=" + uniqueId+"_"+client_version;
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`获取待处理的列表采集任务失败，状态码: ${response.status}`);
    }
    const data = await response.json();
    console.log(ct(), url + ",获取待处理的列表采集任务成功:", data.data);
     // 等待chrome.storage.local.set操作完成
     await chrome.storage.local.remove("listTask");
     await chrome.storage.local.set({ listTask: data.data });
     console.log(ct(), "列表任务数据已成功写入存储,",data.data);
    return data.data;
  } catch (error) {
    erpErrLog("获取待处理的列表采集任务失败:", error);
    return null;
  }
}


// 提取Amazon搜索结果HTML内容
function extractAmazonSearchResults() {
  const searchResultsContainer = document.querySelector('span[data-component-type="s-search-results"]');
  if (!searchResultsContainer) {
    console.warn(ct() + "未找到Amazon搜索结果容器");
    // 尝试其他可能的选择器
    const alternativeContainer = document.querySelector('[data-component-type="s-search-results"]') ||
                                document.querySelector('.s-search-results') ||
                                document.querySelector('#search');
    if (alternativeContainer) {
      console.log(ct() + "使用备用选择器找到搜索结果容器");
      return {
        html: alternativeContainer.outerHTML,
        url: window.location.href,
        timestamp: new Date().toISOString()
      };
    }
    return null;
  }

  return {
    html: searchResultsContainer.outerHTML,
    url: window.location.href,
    timestamp: new Date().toISOString()
  };
}

// 清理搜索结果HTML，剔除无关代码
function cleanSearchResultsHtml(html) {
  try {
    // 创建临时DOM元素来处理HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;

    // 移除脚本标签
    const scripts = tempDiv.querySelectorAll('script');
    scripts.forEach(script => script.remove());

    // 移除样式标签
    const styles = tempDiv.querySelectorAll('style');
    styles.forEach(style => style.remove());

    // 移除注释节点
    const walker = document.createTreeWalker(
      tempDiv,
      NodeFilter.SHOW_COMMENT,
      null,
      false
    );
    const comments = [];
    let node;
    while (node = walker.nextNode()) {
      comments.push(node);
    }
    comments.forEach(comment => comment.remove());

    // 移除一些无关的属性
    const allElements = tempDiv.querySelectorAll('*');
    allElements.forEach(element => {
      // 移除事件处理属性
      const attributes = [...element.attributes];
      attributes.forEach(attr => {
        if (attr.name.startsWith('on') ||
            attr.name.includes('data-testid') ||
            attr.name.includes('data-analytics')) {
          element.removeAttribute(attr.name);
        }
      });
    });

    return tempDiv.innerHTML;
  } catch (error) {
    console.warn(ct() + "清理HTML时出错，返回原始HTML:", error);
    return html;
  }
}

// 计算总页数
function calculateTotalPages() {
  try {
    // 尝试从分页导航中获取总页数
    const paginationSelectors = [
      '.a-pagination .a-normal:last-of-type',
      '.s-pagination-strip .s-pagination-item:last-of-type',
      '[data-testid="pagination"] span:last-of-type'
    ];

    for (const selector of paginationSelectors) {
      const lastPageElement = document.querySelector(selector);
      if (lastPageElement) {
        const pageText = lastPageElement.textContent.trim();
        const pageNumber = parseInt(pageText);
        if (!isNaN(pageNumber)) {
          console.log(ct() + "从分页导航获取总页数:", pageNumber);
          return pageNumber;
        }
      }
    }

    // 如果无法从分页导航获取，尝试从URL参数或其他方式估算
    const currentPage = getCurrentPageNumber();
    const hasNext = hasNextPage();

    if (!hasNext) {
      // 如果没有下一页，当前页就是最后一页
      return currentPage;
    } else {
      // 如果有下一页，返回一个估算值（当前页+10，实际会在采集过程中更新）
      return currentPage + 10;
    }
  } catch (error) {
    console.warn(ct() + "计算总页数时出错:", error);
    return 1;
  }
}

// 上传列表采集任务结果
async function uploadListTaskResult(listTask, searchResultsHtml, isCompleted = false) {
  try {
    // 获取当前页码和计算总页数
    const currentPage = getCurrentPageNumber();
    const totalPages = calculateTotalPages();

    // 将HTML内容进行Base64编码，并剔除无关的HTML代码
    const cleanedHtml = cleanSearchResultsHtml(searchResultsHtml);
    const base64Html = btoa(encodeURIComponent(cleanedHtml).replace(/%([0-9A-F]{2})/g, (_, p1) => String.fromCharCode(parseInt(p1, 16))));

    const payload = {
      id: listTask.id,
      url: window.location.href,
      htmlContent: base64Html,
      currentPage: currentPage,
      totalPages: totalPages
    };

    const response = await fetch(LIST_TASK_SUBMIT_URL, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    });

    console.log(ct(),"uploadListTaskResult 参数:" , payload);
    const resp = await response.json();
    console.log(ct(),"uploadListTaskResult 上传成功:",resp);

    // 更新本地存储的任务信息
    if (!isCompleted) {
      listTask.crawled_pages = (listTask.crawled_pages || 0) + 1;
      await chrome.storage.local.set({ listTask: listTask });
    } else {
      // 任务完成，清理本地缓存
      await chrome.storage.local.remove("listTask");
      console.log(ct(),"列表任务完成，清理本地缓存");
    }

    return resp;
  } catch (error) {
    erpErrLog("上传列表采集任务结果失败:", error);
    throw error;
  }
}

async function getLocalStorageData(keys) {
  return new Promise((resolve, reject) => {
    chrome.storage.local.get(keys, (result) => {
      if (chrome.runtime.lastError) {
        reject(chrome.runtime.lastError);
      } else {
        resolve(result);
      }
    });
  });
}

//Amazon列表页采集
/**
 * Amazon列表页采集函数
 * 该函数用于自动采集Amazon搜索结果页面的商品信息，并处理分页以实现自动翻页采集。
 * 基于后台数据库任务表 zz_amazon_list_tasks 进行任务管理
 * 无需参数。
 * 无返回值。
 */
async function amazon_list_caiji() {
  var message = {
    action: "can_zidong_caiji",
    info: "",
  };

  chrome.runtime.sendMessage(message, async (msg) => {
    console.log(ct(),"列表采集答复", msg);
    if (chrome.runtime.lastError) {
      console.warn("消息发送失败:", chrome.runtime.lastError.message);
      return;
    }
    
    let msgobj = JSON.parse(msg);
    console.log(msgobj["state"]);
    
    if (msgobj["state"] == 0) {
      console.log(ct(),"Amazon列表页采集中....");

      //判断listTask 是否存在，如果不存在，则去获取待采集的任务
      var result;
      var listTask;
      try {
        result = await getLocalStorageData(["listTask"]);
        console.log(ct(),"获取本地存储信息：",JSON.stringify(result));
      } catch (error) {
        erpErrLog("获取本地存储数据失败:", error);
      }

      if (result == undefined || result["listTask"] == undefined) {
        // 获取新的列表采集任务
        listTask = await getPendingListTasks(msgobj["uniqueId"]);        if (listTask && listTask.url) {
          console.log(ct(),"获取到新的列表采集任务，准备跳转：",listTask.url,",TASK:" ,JSON.stringify(listTask));
          // 任务状态从pending变为processing在后台API中处理
          
          // 模拟用户行为后再跳转
          await simulateUserBrowsingBehavior();
          
          const waitTime = util_get_random_number(3000, 8000); // 随机等待3-8秒
          console.log(ct(), `模拟行为完成，等待 ${waitTime/1000} 秒后跳转到任务页面`);
          
          setTimeout(() => {
            location.href = listTask.url;
          }, waitTime);
          return;} else {
          console.log(ct(),"暂无待处理的列表采集任务");
          
          // 等待一段时间后再次检查是否有新任务
          const waitTime = util_get_random_number(30000, 60000); // 无任务时等待30-60秒
          console.log(ct(),"暂无任务，等待 " + (waitTime/1000) + " 秒后重新检查...");
          
          setTimeout(async () => {
            console.log(ct(),"重新检查是否有新的列表采集任务...");
            amazon_list_caiji();
          }, waitTime);
          
          return;
        }
      } else {
        listTask = result["listTask"];
        console.log(ct(),"继续处理现有列表采集任务：", JSON.stringify(listTask));
      }

      console.log(ct(),"当前列表采集任务："+JSON.stringify(listTask));

      let current_url = window.location.href;
      
      // 检查当前URL是否匹配任务URL
      if (listTask.url && !isUrlMatch(current_url, listTask.url)) {
        // 如果当前页面是Amazon搜索页面且有搜索结果，允许继续采集
        if (isAmazonSearchPage(current_url)) {
          const searchResultsContainer = document.querySelector('span[data-component-type="s-search-results"]') ||
                                        document.querySelector('[data-component-type="s-search-results"]') ||
                                        document.querySelector('.s-search-results');
          if (searchResultsContainer) {
            console.log(ct(),"当前页面是有效的Amazon搜索页面，继续在此页面采集");
            // 更新任务URL为当前URL，以便后续页面匹配
            listTask.url = current_url.split('&page=')[0].split('?page=')[0]; // 移除页码参数
            await chrome.storage.local.set({ listTask: listTask });          } else {
            console.log(ct(),"当前页面URL与任务URL不匹配且无搜索结果，跳转至任务页面：", listTask.url);
            
            // 模拟用户行为后再跳转
            await simulateUserBrowsingBehavior();
            
            const waitTime = util_get_random_number(3000, 8000); // 随机等待3-8秒
            console.log(ct(), `模拟行为完成，等待 ${waitTime/1000} 秒后跳转到任务页面`);
            
            setTimeout(() => {
              location.href = listTask.url;
            }, waitTime);
            return;
          }        } else {
          console.log(ct(),"当前页面不是Amazon搜索页面，跳转至任务页面：", listTask.url);
          
          // 模拟用户行为后再跳转
          await simulateUserBrowsingBehavior();
          
          const waitTime = util_get_random_number(3000, 8000); // 随机等待3-8秒
          console.log(ct(), `模拟行为完成，等待 ${waitTime/1000} 秒后跳转到任务页面`);
          
          setTimeout(() => {
            location.href = listTask.url;
          }, waitTime);
          return;
        }
      }

      if (current_url && isAmazonSearchPage(current_url)) {
        console.log(ct(),"处理Amazon列表页采集中...");
        
        // 提取搜索结果HTML
        const searchResultsData = extractAmazonSearchResults();
        if (!searchResultsData) {
          console.warn(ct()+"未找到搜索结果数据");
          return;
        }        // 检查是否已达到最大页数
        const currentPage = getCurrentPageNumber();
        const maxPages = listTask.maxPagesToCrawl || 400; // 如果未定义则默认为5页
        const isLastPage = currentPage >= maxPages || !hasNextPage();
        
        console.log(ct(),"当前页码：", currentPage, "最大页数：", maxPages, "是否最后一页：", isLastPage);

        try {
          // 上传当前页面的搜索结果
          await uploadListTaskResult(listTask, searchResultsData.html, isLastPage);
          console.log(ct(),"页面数据上传成功");          if (isLastPage) {
            console.log(ct(),"已达到最大页数或最后一页，列表采集任务完成");
            await chrome.storage.local.remove("listTask");
            
            // 任务完成后，先模拟用户行为再等待获取下一个任务
            await simulateUserBrowsingBehavior();
            
            const waitTime = util_get_random_number(10000, 20000); // 随机等待10-20秒
            console.log(ct(),"当前任务完成，等待 " + (waitTime/1000) + " 秒后获取下一个任务");
            
            setTimeout(async () => {
              console.log(ct(),"开始获取下一个列表采集任务...");
              // 重新调用采集函数来获取新任务
              amazon_list_caiji();
            }, waitTime);} else {
            // 继续下一页 - 先模拟用户行为
            const nextPageUrl = getNextPageUrl();
            if (nextPageUrl) {
              console.log(ct(),"准备跳转到下一页：", nextPageUrl);
              
              // 先模拟用户浏览行为
              await simulateUserBrowsingBehavior();
              
              // 再等待随机时间后跳转
              const waitTime = util_get_random_number(5000, 10000); // 随机延迟5-10秒
              console.log(ct(), `模拟行为完成，等待 ${waitTime/1000} 秒后跳转到下一页`);
              
              setTimeout(() => {
                location.href = nextPageUrl;
              }, waitTime);
            } else {
              console.log(ct(),"未找到下一页链接，列表采集任务结束");
              await uploadListTaskResult(listTask, searchResultsData.html, true);
              await chrome.storage.local.remove("listTask");
              
              // 任务完成后，先模拟用户行为再等待获取下一个任务
              await simulateUserBrowsingBehavior();
              
              const waitTime = util_get_random_number(10000, 20000); // 随机等待10-20秒
              console.log(ct(),"当前任务完成，等待 " + (waitTime/1000) + " 秒后获取下一个任务");
              
              setTimeout(async () => {
                console.log(ct(),"开始获取下一个列表采集任务...");
                // 重新调用采集函数来获取新任务
                amazon_list_caiji();
              }, waitTime);
            }
          }        } catch (error) {
          erpErrLog("上传搜索结果数据失败:", error);
          // 错误处理：标记任务失败并清理本地缓存
          try {
            await uploadListTaskResult(listTask, searchResultsData.html, true);
            await chrome.storage.local.remove("listTask");
            console.log(ct(),"任务因错误而结束，已清理本地缓存");
            
            // 出错时也模拟用户行为
            await simulateUserBrowsingBehavior();
            
            // 即使出错也要等待一段时间再获取下一个任务
            const waitTime = util_get_random_number(15000, 25000); // 出错时等待更长时间15-25秒
            console.log(ct(),"任务出错，等待 " + (waitTime/1000) + " 秒后获取下一个任务");
            
            setTimeout(async () => {
              console.log(ct(),"开始获取下一个列表采集任务...");
              // 重新调用采集函数来获取新任务
              amazon_list_caiji();
            }, waitTime);
          } catch (cleanupError) {
            erpErrLog("清理任务缓存失败:", cleanupError);
            
            // 即使清理失败也要尝试获取下一个任务
            const waitTime = util_get_random_number(20000, 30000); // 清理失败时等待更长时间20-30秒
            console.log(ct(),"清理失败，等待 " + (waitTime/1000) + " 秒后获取下一个任务");
            
            setTimeout(async () => {
              console.log(ct(),"开始获取下一个列表采集任务...");
              // 强制清理本地存储并重新开始
              await chrome.storage.local.remove("listTask");
              amazon_list_caiji();
            }, waitTime);
          }
        }
      }
    } else {
      console.log(ct(),"==========已暂停自动列表采集==============");
    }
  });
}

// 原有的旧版本采集函数（已废弃，保留以防兼容性问题）
async function amazon_offer_caiji() {
  console.log(ct() + "amazon_offer_caiji函数已废弃，请使用amazon_list_caiji函数");
  // 直接调用新的列表采集函数
  return amazon_list_caiji();
}

// 封装请求的方法
async function sendPutRequest(url, jsonMessage, headers, retries = 5) {
  const options = {
    method: "PUT",
    headers: headers,
    body: jsonMessage,
  };

  for (let i = 0; i < retries; i++) {
    try {
      const response = await fetch(url, options);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.warn(`Attempt ${i + 1} failed:`, error);
      if (i < retries - 1) {
        console.log(`Retrying... (${i + 2}/${retries})`);
        await new Promise((res) => setTimeout(res, 20000));
      } else {
        throw error;
      }
    }
  }
}

// 监察页面性能资源加载情况，并根据特定条件记录或执行某些操作。
function perf_observer(list, observer) {
  performanceResourceList = list.getEntriesByType("resource");

  for (i = 0; i < performanceResourceList.length; i++) {
    performanceResourceItem = performanceResourceList[i];

    if (performanceResourceItem["initiatorType"] == "xmlhttprequest") {
      // Amazon相关的性能监控逻辑可以在这里添加
      if (
        performanceResourceItem["name"].indexOf("amazon.com") != -1 &&
        performanceResourceItem["name"].indexOf("doubleclick") == -1 &&
        performanceResourceItem["name"].indexOf("google-analytics") == -1
      ) {
        console.log(ct() + "Amazon API请求: " + performanceResourceItem["name"]);
      }
    }
  }
}

var observer_ = new PerformanceObserver(perf_observer);
observer_.observe({ entryTypes: ["resource", "navigation", "paint"] });

$(document).ready(function () {
  console.log(ct(),"文档准备就绪时执行的函数 ready");

  chrome.runtime.sendMessage({action: "updateTime"}, response => {
    console.log(ct(),"更新时间响应值：",response.state);
  });

  //判断是否是blocked 页面
  // 获取页面中所有的<script>标签
  var scripts = document.getElementsByTagName('script');
  
  // 检查每个<script>标签的src属性
  for (var i = 0; i < scripts.length; i++) {
    var src = scripts[i].getAttribute('src');
    if (src && src.includes('https://ct.captcha-delivery.com/c.js')) {
     console.warn(ct()+"页面被屏蔽，等待10分钟。");
   
    // 如果找到了匹配的<script>标签，等待10分钟后刷新页面
      setTimeout(function() {
        window.location.reload();
      }, 600000); // 10分钟的毫秒数（600秒 * 1000毫秒）
      return; // 退出循环
    }
    // Amazon验证码检查可以在这里添加
    if (src && src.includes('amazon.com') && src.includes('captcha')) {
     console.warn(ct()+"页面出现Amazon验证码，等待10分钟。");

    // 如果找到了匹配的<script>标签，等待10分钟后刷新页面
      setTimeout(function() {
        window.location.reload();
      }, 600000); // 10分钟的毫秒数（600秒 * 1000毫秒）
      return; // 退出循环
    }
  }

  // 启动Amazon列表页采集
  setTimeout(function () {
    amazon_list_caiji();
  }, 5000);
});

// 发送消息给弹窗
function send_message_to_popup(message = {}) {
  console.log(ct(),"message");
  console.log(ct(),message);
  chrome.runtime.sendMessage(message, (msg) => {
    if (chrome.runtime.lastError) {
      erpErrLog("消息发送失败:", chrome.runtime.lastError.message);
    }
    // if (message.shangjia_infos.length > 0) {
    //     // alert('该页卖家已成功添加到采集队列');
    // }
    console.log(ct(),"答复：",msg);
  });
}

function downloadIamge(imgsrc, name) {
  let image = new Image();
  image.setAttribute("crossOrigin", "anonymous");
  image.onload = function () {
    let canvas = document.createElement("canvas");
    canvas.width = image.width;
    canvas.height = image.height;
    let context = canvas.getContext("2d");
    context.drawImage(image, 0, 0, image.width, image.height);
    let url = canvas.toDataURL("image/jpeg");
    let a = document.createElement("a");
    let event = new MouseEvent("click");
    a.download = name || "photo";
    a.href = url;
    a.dispatchEvent(event);
  };
  image.src = imgsrc;
}

// Amazon产品图片下载功能（如需要可以重新实现）
function get_product_lunbo_imgs() {
  console.log(ct() + "Amazon产品轮播图下载功能待实现");
  // Amazon页面的图片选择器需要重新分析和实现
  return [];
}

function get_product_details_imgs() {
  console.log(ct() + "Amazon产品详情图下载功能待实现");
  // Amazon页面的图片选择器需要重新分析和实现
  return [];
}

chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action == "extract-product-lunbo-imgs") {
    console.log(ct(),request.info);
    catalog_products_url = get_product_lunbo_imgs();
    sendResponse(catalog_products_url);
  } else if (request.action == "extract-product-details-imgs") {
    console.log(ct(),request.info);
    catalog_products_url = get_product_details_imgs();
    sendResponse(catalog_products_url);
  } else if (request.action === "amazon_list_caiji") {
    amazon_list_caiji();
    sendResponse({ status: "amazon_list_caiji 方法已执行" });
  } else if (request.action === "getPageData") {
    // 返回当前页面的HTML数据
    sendResponse({
      html: document.documentElement.outerHTML,
      url: window.location.href,
      title: document.title
    });
  } else if (request.action === "fetchSkuDetails") {
    // 获取SKU详情（通过iframe或新窗口）
    fetchSkuDetailsFromUrl(request.asin, request.url).then(details => {
      sendResponse(details);
    }).catch(error => {
      console.error("获取SKU详情失败:", error);
      sendResponse(null);
    });
    return true; // 异步响应
  }
  return true;
});

/**
 * 从指定URL获取SKU详情
 * @param {string} asin SKU的ASIN
 * @param {string} url SKU详情页URL
 * @returns {Promise<Object|null>} SKU详情数据
 */
async function fetchSkuDetailsFromUrl(asin, url) {
  try {
    console.log(ct() + `开始获取SKU ${asin} 的详情...`);

    // 创建隐藏的iframe来获取SKU页面
    const iframe = document.createElement('iframe');
    iframe.style.display = 'none';
    iframe.style.width = '0';
    iframe.style.height = '0';
    document.body.appendChild(iframe);

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        document.body.removeChild(iframe);
        reject(new Error('获取SKU详情超时'));
      }, 15000); // 15秒超时

      iframe.onload = () => {
        try {
          const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;

          // 提取SKU详情
          const skuDetails = {
            asin: asin,
            price: null,
            stockStatus: 'Unknown',
            imageUrl: null
          };

          // 提取价格
          const priceElement = iframeDoc.querySelector('.a-price .a-offscreen');
          if (priceElement) {
            const priceText = priceElement.textContent.trim();
            const priceMatch = priceText.replace('$', '').match(/[\d,]+\.?\d*/);
            if (priceMatch) {
              skuDetails.price = parseFloat(priceMatch[0].replace(',', ''));
            }
          }

          // 提取库存状态
          const stockSelectors = [
            '#availability span',
            '#availabilityInsideBuyBox_feature_div span',
            '.a-color-success',
            '.a-color-state'
          ];

          for (const selector of stockSelectors) {
            const availabilityElement = iframeDoc.querySelector(selector);
            if (availabilityElement) {
              const availabilityText = availabilityElement.textContent.trim();
              if (availabilityText && availabilityText.length > 0) {
                if (availabilityText.includes('In Stock')) {
                  skuDetails.stockStatus = 'In Stock';
                } else if (availabilityText.includes('Out of Stock')) {
                  skuDetails.stockStatus = 'Out of Stock';
                } else {
                  skuDetails.stockStatus = availabilityText.substring(0, 50);
                }
                break;
              }
            }
          }

          // 提取主图
          const mainImageElement = iframeDoc.querySelector('#landingImage, #imgBlkFront');
          if (mainImageElement) {
            skuDetails.imageUrl = mainImageElement.src || mainImageElement.getAttribute('data-src');
          }

          clearTimeout(timeout);
          document.body.removeChild(iframe);

          console.log(ct() + `SKU ${asin} 详情获取成功:`, skuDetails);
          resolve(skuDetails);

        } catch (error) {
          clearTimeout(timeout);
          document.body.removeChild(iframe);
          console.error(ct() + `解析SKU ${asin} 详情失败:`, error);
          resolve(null);
        }
      };

      iframe.onerror = () => {
        clearTimeout(timeout);
        document.body.removeChild(iframe);
        reject(new Error(`加载SKU页面失败: ${url}`));
      };

      // 设置iframe源
      iframe.src = url;
    });

  } catch (error) {
    console.error(ct() + `获取SKU ${asin} 详情时出错:`, error);
    return null;
  }
}

function getCategoryIDFromURL() {
  // 获取当前页面的URL
  const url = window.location.href;
  const urlObj = new URL(url);

  // 从Amazon URL中提取类别ID
  // Amazon URL格式示例: /s?rh=n%3A274325011 或 /gp/browse.html?node=274325011
  const searchParams = urlObj.searchParams;

  // 尝试从rh参数中提取node ID
  const rhParam = searchParams.get('rh');
  if (rhParam) {
    const nodeMatch = rhParam.match(/n%3A(\d+)|n:(\d+)/);
    if (nodeMatch) {
      return nodeMatch[1] || nodeMatch[2];
    }
  }

  // 尝试从node参数中提取
  const nodeParam = searchParams.get('node');
  if (nodeParam) {
    return nodeParam;
  }

  // 如果都没找到，返回null
  return null;
}

// 使用自定义函数解析查询字符串并返回参数对象
function util_getCurrentUrlQueryStringParams() {
  var queryString = window.location.search;
  var params = {};
  queryString = queryString.substring(1);
  var keyValuePairs = queryString.split("&");
  keyValuePairs.forEach(function (keyValuePair) {
    var pair = keyValuePair.split("=");
    var key = decodeURIComponent(pair[0]);
    var value = decodeURIComponent(pair[1] || "");
    
    // 如果params对象中已经存在这个键，则将值添加到数组中
    // 否则，创建一个新数组并添加值
    if (params.hasOwnProperty(key)) {
      if (Array.isArray(params[key])) {
        params[key].push(value);
      } else {
        // 如果之前存储的不是数组，则转换成数组
        params[key] = [params[key], value];
      }
    } else {
      params[key] = value;
    }
  });
  return params;
}


function util_get_random_number(min = 1000, max = 5000) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}


function erpErrLog(msg) {
  console.error(ct()+msg);
  
}

function ct() {
  var now = new Date();
  var year = now.getFullYear();
  var month = now.getMonth() + 1; // 月份是从0开始的
  var day = now.getDate();
  var hours = now.getHours();
  var minutes = now.getMinutes();
  var seconds = now.getSeconds();
  
  // 补零函数
  function pad(number) {
    return (number < 10 ? '0' : '') + number;
  }

  // 返回自定义格式的日期时间字符串
  return year + '-' + pad(month) + '-' + pad(day) + ' ' + pad(hours) + ':' + pad(minutes) + ':' + pad(seconds) +' ';
}

// Amazon页面检测和辅助函数

// 检查是否为Amazon搜索页面
function isAmazonSearchPage(url) {
  // 检查是否为Amazon域名
  if (!url.includes('amazon.com')) {
    return false;
  }

  // 检查是否为搜索页面或列表页面
  return url.includes('amazon.com/s') ||
         url.includes('amazon.com/gp/search') ||
         url.includes('/s?') ||
         url.includes('/gp/search') ||
         (url.includes('amazon.com') && (url.includes('k=') || url.includes('keywords=')));
}

// 检查两个URL是否匹配（忽略动态参数）
function isUrlMatch(currentUrl, taskUrl) {
  try {
    const current = new URL(currentUrl);
    const task = new URL(taskUrl);

    // 检查域名和路径是否匹配
    if (current.hostname !== task.hostname || current.pathname !== task.pathname) {
      return false;
    }

    // 定义需要比较的核心搜索参数
    const coreParams = ['rh', 'i', 'k', 'keywords', 'field-keywords', 'url', 'node'];

    // 比较核心参数
    for (const param of coreParams) {
      const currentValue = current.searchParams.get(param);
      const taskValue = task.searchParams.get(param);

      // 如果任一URL有该参数，则必须匹配
      if (currentValue !== taskValue) {
        // 特殊处理：如果都没有该参数，则认为匹配
        if (currentValue === null && taskValue === null) {
          continue;
        }
        console.log(ct() + `URL参数不匹配: ${param}, 当前: ${currentValue}, 任务: ${taskValue}`);
        return false;
      }
    }

    console.log(ct() + "URL匹配成功");
    return true;
  } catch (error) {
    console.error(ct() + "URL匹配检查出错:", error);
    // 如果URL解析出错，使用简单的字符串包含检查
    return currentUrl.includes(taskUrl.split('?')[0]) || taskUrl.includes(currentUrl.split('?')[0]);
  }
}

// 获取当前页码
function getCurrentPageNumber() {
  const urlParams = new URLSearchParams(window.location.search);
  const page = urlParams.get('page');
  return page ? parseInt(page) : 1;
}

// 检查是否有下一页
function hasNextPage() {
  // 尝试多种选择器来查找下一页链接
  const nextPageSelectors = [
    'a[aria-label*="next page"]',
    'a[aria-label*="Next page"]',
    '.s-pagination-next:not(.s-pagination-disabled)',
    'a.s-pagination-next:not(.s-pagination-disabled)',
    'a[aria-label="Go to next page"]',
    '.a-pagination .a-last:not(.a-disabled)',
    'a[data-action="s-show-more-results"]'
  ];

  for (const selector of nextPageSelectors) {
    const nextPageLink = document.querySelector(selector);
    if (nextPageLink && !nextPageLink.classList.contains('s-pagination-disabled') &&
        !nextPageLink.classList.contains('a-disabled')) {
      return true;
    }
  }

  return false;
}

// 获取下一页URL
function getNextPageUrl() {
  // 尝试多种选择器来查找下一页链接
  const nextPageSelectors = [
    'a[aria-label*="next page"]',
    'a[aria-label*="Next page"]',
    '.s-pagination-next:not(.s-pagination-disabled)',
    'a.s-pagination-next:not(.s-pagination-disabled)',
    'a[aria-label="Go to next page"]',
    '.a-pagination .a-last:not(.a-disabled)',
    'a[data-action="s-show-more-results"]'
  ];

  for (const selector of nextPageSelectors) {
    const nextPageLink = document.querySelector(selector);
    if (nextPageLink && nextPageLink.href &&
        !nextPageLink.classList.contains('s-pagination-disabled') &&
        !nextPageLink.classList.contains('a-disabled')) {
      console.log(ct() + "找到下一页链接:", nextPageLink.href);
      return nextPageLink.href;
    }
  }

  // 如果没找到下一页链接，手动构造
  const currentPage = getCurrentPageNumber();
  const url = new URL(window.location.href);
  url.searchParams.set('page', (currentPage + 1).toString());
  console.log(ct() + "手动构造下一页URL:", url.toString());
  return url.toString();
}

// 模拟用户行为函数

// 模拟随机鼠标移动
function simulateRandomMouseMovement() {
  return new Promise((resolve) => {
    let moveCount = 0;
    const totalMoves = util_get_random_number(3, 8); // 随机移动3-8次
    
    const moveInterval = setInterval(() => {
      const x = Math.random() * window.innerWidth;
      const y = Math.random() * window.innerHeight;
      
      // 创建鼠标移动事件
      const mouseMoveEvent = new MouseEvent('mousemove', {
        view: window,
        bubbles: true,
        cancelable: true,
        clientX: x,
        clientY: y
      });
      
      document.dispatchEvent(mouseMoveEvent);
      console.log(ct(), `模拟鼠标移动到 (${Math.round(x)}, ${Math.round(y)})`);
      
      moveCount++;
      if (moveCount >= totalMoves) {
        clearInterval(moveInterval);
        resolve();
      }
    }, util_get_random_number(200, 800)); // 每次移动间隔200-800ms
  });
}

// 模拟随机页面滚动
function simulateRandomScrolling() {
  return new Promise((resolve) => {
    let scrollCount = 0;
    const totalScrolls = util_get_random_number(2, 5); // 随机滚动2-5次
    
    const scrollInterval = setInterval(() => {
      const scrollY = Math.random() * (document.body.scrollHeight - window.innerHeight);
      
      window.scrollTo({
        top: scrollY,
        behavior: 'smooth'
      });
      
      console.log(ct(), `模拟滚动到位置: ${Math.round(scrollY)}`);
      
      scrollCount++;
      if (scrollCount >= totalScrolls) {
        clearInterval(scrollInterval);
        // 最后滚动回顶部
        setTimeout(() => {
          window.scrollTo({ top: 0, behavior: 'smooth' });
          console.log(ct(), "滚动回页面顶部");
          setTimeout(resolve, 500); // 等待滚动完成
        }, 1000);
      }
    }, util_get_random_number(1000, 2000)); // 每次滚动间隔1-2秒
  });
}

// 模拟随机点击商品（不实际导航）
function simulateRandomProductClicks() {
  return new Promise((resolve) => {
    const productLinks = document.querySelectorAll('[data-asin] h3 a, .s-result-item h3 a');
    if (productLinks.length === 0) {
      resolve();
      return;
    }
    
    const clickCount = Math.min(util_get_random_number(1, 3), productLinks.length); // 随机点击1-3个商品
    let clicked = 0;
    
    const clickInterval = setInterval(() => {
      if (clicked < clickCount) {
        const randomIndex = Math.floor(Math.random() * productLinks.length);
        const link = productLinks[randomIndex];
        
        // 创建鼠标悬停事件而不是实际点击
        const mouseOverEvent = new MouseEvent('mouseover', {
          view: window,
          bubbles: true,
          cancelable: true
        });
        
        link.dispatchEvent(mouseOverEvent);
        console.log(ct(), `模拟鼠标悬停在商品: ${link.textContent.substring(0, 50)}...`);
        
        // 模拟鼠标离开
        setTimeout(() => {
          const mouseOutEvent = new MouseEvent('mouseout', {
            view: window,
            bubbles: true,
            cancelable: true
          });
          link.dispatchEvent(mouseOutEvent);
        }, util_get_random_number(500, 1500));
        
        clicked++;
      } else {
        clearInterval(clickInterval);
        resolve();
      }
    }, util_get_random_number(1500, 3000)); // 每次操作间隔1.5-3秒
  });
}

// 综合模拟用户浏览行为
async function simulateUserBrowsingBehavior() {
  console.log(ct(), "开始模拟用户浏览行为...");
  
  try {
    // 随机执行各种用户行为
    const behaviors = [];
    
    // 总是包含鼠标移动
    behaviors.push(simulateRandomMouseMovement());
    
    // 50%概率滚动页面
    if (Math.random() > 0.5) {
      behaviors.push(simulateRandomScrolling());
    }
    
    // 30%概率模拟商品悬停
    if (Math.random() > 0.7) {
      behaviors.push(simulateRandomProductClicks());
    }
    
    // 并行执行一些行为，串行执行另一些
    await Promise.all(behaviors.slice(0, 1)); // 先执行鼠标移动
    
    if (behaviors.length > 1) {
      for (let i = 1; i < behaviors.length; i++) {
        await behaviors[i];
        // 行为之间随机等待
        await new Promise(resolve => setTimeout(resolve, util_get_random_number(500, 1500)));
      }
    }
    
    console.log(ct(), "用户浏览行为模拟完成");
  } catch (error) {
    console.warn(ct(), "模拟用户行为时出错:", error);
  }
}
