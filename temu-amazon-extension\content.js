var BASE_URL = "https://51erp.store/xace-web/api/amazon";
 //var BASE_URL = "http://127.0.0.1:32000/api/amazon";

var TASK_WAITGET_URL = BASE_URL + "/task/waitGets";
var TASK_SUBMIT_URL = BASE_URL + "/task/submitResult";
var LIST_TASK_WAITGET_URL = BASE_URL + "/listTask/waitGets";
var LIST_TASK_SUBMIT_URL = BASE_URL + "/listTask/submitResult";
var client_version = chrome.runtime.getManifest().version;

// 从服务器获取待处理的Amazon列表采集任务
async function getPendingListTasks(uniqueId) {
  try {
    var url = LIST_TASK_WAITGET_URL + "?clientId=" + uniqueId+"_"+client_version;
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`获取待处理的列表采集任务失败，状态码: ${response.status}`);
    }
    const data = await response.json();
    console.log(ct(), url + ",获取待处理的列表采集任务成功:", data.data);
     // 等待chrome.storage.local.set操作完成
     await chrome.storage.local.remove("listTask");
     await chrome.storage.local.set({ listTask: data.data });
     console.log(ct(), "列表任务数据已成功写入存储,",data.data);
    return data.data;
  } catch (error) {
    erpErrLog("获取待处理的列表采集任务失败:", error);
    return null;
  }
}


// 提取Amazon搜索结果HTML内容
function extractAmazonSearchResults() {
  const searchResultsContainer = document.querySelector('span[data-component-type="s-search-results"]');
  if (!searchResultsContainer) {
    console.warn(ct() + "未找到Amazon搜索结果容器");
    return null;
  }
  
  return {
    html: searchResultsContainer.outerHTML,
    url: window.location.href,
    timestamp: new Date().toISOString()
  };
}

// 上传已处理完成的列表任务结果
async function uploadListTaskResult(task, searchResultsHtml, isCompleted = false) {
  try {
    const payload = {
      taskId: task.id,
      url: window.location.href,
      searchResultsHtml: searchResultsHtml,
      crawledPages: (task.crawled_pages || 0) + 1,
      isCompleted: isCompleted,
      timestamp: new Date().toISOString()
    };

    const response = await fetch(LIST_TASK_SUBMIT_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    });
    
    console.log(ct(),"uploadListTaskResult 参数:" , payload);
    const resp = await response.json();
    console.log(ct(),"uploadListTaskResult 上传成功:",resp);

    // 清理本地缓存
    if (isCompleted) {
      await chrome.storage.local.remove("listTask");
      console.log(ct(),"列表任务完成，清理本地缓存");
    }

    return resp;
  } catch (error) {
    erpErrLog("上传列表任务结果失败:", error);
    throw error;
  }
}

// 上传列表采集任务结果
async function uploadListTaskResult(listTask, searchResultsHtml, isCompleted = false) {
  try {
    const payload = {
      taskId: listTask.id,
      url: window.location.href,
      searchResultsHtml: searchResultsHtml,
      crawledPages: (listTask.crawled_pages || 0) + 1,
      isCompleted: isCompleted,
      timestamp: new Date().toISOString()
    };

    const response = await fetch(TASK_SUBMIT_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    });
    
    console.log(ct(),"uploadListTaskResult 参数:" , payload);
    const resp = await response.json();
    console.log(ct(),"uploadListTaskResult 上传成功:",resp);

    // 更新本地存储的任务信息
    if (!isCompleted) {
      listTask.crawled_pages = (listTask.crawled_pages || 0) + 1;
      await chrome.storage.local.set({ listTask: listTask });
    }

    return resp;
  } catch (error) {
    erpErrLog("上传列表采集任务结果失败:", error);
    throw error;
  }
}

async function getLocalStorageData(keys) {
  return new Promise((resolve, reject) => {
    chrome.storage.local.get(keys, (result) => {
      if (chrome.runtime.lastError) {
        reject(chrome.runtime.lastError);
      } else {
        resolve(result);
      }
    });
  });
}

//Amazon列表页采集
/**
 * Amazon列表页采集函数
 * 该函数用于自动采集Amazon搜索结果页面的商品信息，并处理分页以实现自动翻页采集。
 * 基于后台数据库任务表 zz_amazon_list_tasks 进行任务管理
 * 无需参数。
 * 无返回值。
 */
async function amazon_list_caiji() {
  var message = {
    action: "can_zidong_caiji",
    info: "",
  };

  chrome.runtime.sendMessage(message, async (msg) => {
    console.log(ct(),"列表采集答复", msg);
    if (chrome.runtime.lastError) {
      console.warn("消息发送失败:", chrome.runtime.lastError.message);
      return;
    }
    
    let msgobj = JSON.parse(msg);
    console.log(msgobj["state"]);
    
    if (msgobj["state"] == 0) {
      console.log(ct(),"Amazon列表页采集中....");

      //判断listTask 是否存在，如果不存在，则去获取待采集的任务
      var result;
      var listTask;
      try {
        result = await getLocalStorageData(["listTask"]);
        console.log(ct(),"获取本地存储信息：",JSON.stringify(result));
      } catch (error) {
        erpErrLog("获取本地存储数据失败:", error);
      }

      if (result == undefined || result["listTask"] == undefined) {
        // 获取新的列表采集任务
        listTask = await getPendingListTasks(msgobj["uniqueId"]);
        if (listTask && listTask.url) {
          console.log(ct(),"获取到新的列表采集任务，准备5s后跳转：",listTask.url,",TASK:" ,JSON.stringify(listTask));
          setTimeout(() => {
            location.href = listTask.url;
          }, 5000);
          return;
        } else {
          console.log(ct(),"暂无待处理的列表采集任务");
          return;
        }
      } else {
        listTask = result["listTask"];
      }

      console.log(ct(),"当前列表采集任务："+JSON.stringify(listTask));

      let current_url = window.location.href;
      
      // 检查当前URL是否匹配任务URL（去除页码参数）
      if (listTask.url && !isUrlMatch(current_url, listTask.url)) {
        console.log(ct(),"当前页面URL与任务URL不匹配，跳转至任务页面,5秒后跳转：", listTask.url);
        setTimeout(() => {
          location.href = listTask.url;
        }, 5000);
        return;
      }

      if (current_url && isAmazonSearchPage(current_url)) {
        console.log(ct(),"处理Amazon列表页采集中...");
        
        // 提取搜索结果HTML
        const searchResultsData = extractAmazonSearchResults();
        if (!searchResultsData) {
          console.warn(ct()+"未找到搜索结果数据");
          return;
        }

        // 检查是否已达到最大页数
        const currentPage = getCurrentPageNumber();
        const isLastPage = currentPage >= listTask.max_pages_to_crawl || !hasNextPage();
        
        console.log(ct(),"当前页码：", currentPage, "最大页数：", listTask.max_pages_to_crawl, "是否最后一页：", isLastPage);

        try {
          // 上传当前页面的搜索结果
          await uploadListTaskResult(listTask, searchResultsData.html, isLastPage);
          console.log(ct(),"页面数据上传成功");

          if (isLastPage) {
            console.log(ct(),"已达到最大页数或最后一页，列表采集任务完成");
            await chrome.storage.local.remove("listTask");
          } else {
            // 继续下一页
            const nextPageUrl = getNextPageUrl();
            if (nextPageUrl) {
              console.log(ct(),"准备跳转到下一页：", nextPageUrl);
              setTimeout(() => {
                location.href = nextPageUrl;
              }, util_get_random_number(3000, 6000)); // 随机延迟3-6秒
            } else {
              console.log(ct(),"未找到下一页链接，列表采集任务结束");
              await uploadListTaskResult(listTask, searchResultsData.html, true);
              await chrome.storage.local.remove("listTask");
            }          }
        } catch (error) {
          erpErrLog("上传搜索结果数据失败:", error);
        }
      }
    } else {
      console.log(ct(),"==========已暂停自动列表采集==============");
    }
  });
}

// 原有的旧版本采集函数（保持兼容性）
async function amazon_offer_caiji() {
  var message = {
    action: "can_zidong_caiji", 
    info: "",
  };

  chrome.runtime.sendMessage(message, async (msg) => {
    console.log(ct(),"offer采集答复", msg);
    if (chrome.runtime.lastError) {
      console.warn("消息发送失败:", chrome.runtime.lastError.message);
      return;
    }
    let msgobj = JSON.parse(msg);
    console.log(msgobj["state"]);
    if (msgobj["state"] == 0) {
      console.log(ct(),"搜索页采集中....");

      //判断task 是否存在，如何不存在，则去获取 待采集的链接
      var result;
      var task;
      try {
        result = await getLocalStorageData(["task"]);
        console.log(ct(),"获取本地存储信息：",JSON.stringify(result));
      } catch (error) {
        erpErrLog("获取本地存储数据失败:", error);
      }

      var isSellerTask = false;  // 类型S  卖家采集，自动采集有销量的数据
      var isCateTask = false; // 类型C  判断是否是目录页采集  只取有数据，按照流行度
      var isCateNewTask = false;  //类型CNEW 需要去新数据，默认取前面5页，根据后台参数来定
      var isProductTask = false; // 产品采集-目录

      if (result == undefined || result["task"] == undefined) {
        task = await getPendingListTasks(msgobj["uniqueId"]);
        if (task && task.url) {
          var linkurl = task.url;

          if (task.next_page_url != undefined) {
            linkurl = task.next_page_url;
          } else {
            linkurl = task.link;
          }

          console.log(ct(),"准备5s后跳转：",linkurl,",TASK:" ,JSON.stringify(task));
          setTimeout(() => {
            location.href = linkurl;
          }, 5000);

          return;
        }
      } else {
        task = result["task"];
      }

      isSellerTask = task.taskType == "S" ? true : false;
      isCateTask = task.taskType == "C" ? true : false;
      isCateNewTask = task.taskType == "CNEW" ? true : false;
      isProductTask = task.taskType == "P" ? true : false;
      var sumCount = 0;

      if(isCateNewTask && task.maxPages>0){
        msgobj["collect_max_page"] = task.maxPages;
        console.log(ct(),"设置最新的最大采集数："+task.maxPages);
      }
      console.log(ct(),"当前采集任务："+JSON.stringify(task));

      

      var isEnd = false;

      if (task.taskType == "C" || task.taskType == "CNEW") {
        nowCategoryId = getCategoryIDFromURL();
        if (task.categoryId != nowCategoryId) {
          var linkurl ="";
          if (task.next_page_url != undefined) {
            linkurl = task.next_page_url;
          } else {
            linkurl = task.link;
          }
          
          console.log(ct(),"当前任务ID与当前页面ID不一致，跳转至新页面,5秒后跳转：",linkurl,",TASK:" + JSON.stringify(task));

          // 延迟一段时间后跳转页面
        setTimeout(() => {
          location.href = linkurl;
          return;
        }, 5000); // 假设我们希望在3秒后跳转


          
        }
      }

      let current_url = window.location.href;
      if (task.taskType == "S"  ) {
          var link = '';
          
          // if (task.next_page_url!=undefined && task.next_page_url != current_url) {
          //   linkurl = task.next_page_url;
          //   console.log(ct(),"当前卖家采集URL和缓存中url不一致，秒后跳转：",linkurl,",TASK:" + JSON.stringify(task));
          //   location.href = linkurl;
          //   return;   
          // } else {
          //   linkurl = task.link;
          // }
          
                 
        }
      



      
      if (current_url) {
        if (1 || current_url.indexOf("search") != -1) {
          var shangjia_infos_upload = [];
          console.log(ct(),"处理offer采集中...");
          task["suppendStatus"] = 0;

          if (isSellerTask) {
            var value = $('span[data-role="counter-value"]').text();
            value = value.replace(/\s/g, "");
            // 如果你需要这个值作为一个数字，可以将其转换为整数
            sumCount = parseInt(value, 10);
            var counterLabelSpan = $('span[data-role="counter-label"]');
            if (counterLabelSpan.parent().next('a').length > 0) {
              // 这里是找到的 a 标签的代码，例如：
              if(counterLabelSpan.parent().next('a').text()=='konto zawieszone'){
                console.warn(ct()+"商家被暂停账号了");
                task["suppendStatus"] = 1;
                result["task"].sellType = 'END';

              }
            }

          } else {


            var value = $("span._24159_R3FS9").text();
            // 由于文本中包含空格，你可能想要移除空格，以便得到一个纯数字的字符串
            value = value.replace(/\s/g, "");
            // 如果你需要这个值作为一个数字，可以将其转换为整数
            sumCount = parseInt(value, 10);
          }
          task["sumCount"] = sumCount;
          console.log(ct(), "当前目录总数: " + sumCount);

          await chrome.storage.local.set({ task: task });


          // if(isCateNewTask || isSellerTask || isProductTask){

            var jsonObject = JSON.parse(util_getscript_listData_content($("body").html()));

            
            if (jsonObject) {
              var elementsArray = jsonObject['__listing_StoreState']['items']['elements'];
              for (let element of elementsArray) {

                if(element.type && element.type=='label'){
                  continue;
                }

                var _buyersQuantity = 0;
                // 检查jsonObject.productPopularity是否存在
              if (element['productPopularity'] && element['productPopularity']['label']) {
                // 使用正则表达式从label中提取数字
                var label = element['productPopularity']['label'];
                var matches = label.match(/\d+/);
                if (matches) {
                  _buyersQuantity = parseInt(matches[0]);
                }
   
              } 
              //产品列表中，会出现卖家的数据 
              if(element.popularityLabel){
                var label = element['popularityLabel'];
                var matches = label.match(/\d+/);
                if (matches) {
                  _buyersQuantity = parseInt(matches[0]);
                }
              }

              var _productOffersCount = 0;
              if(element['productOffersCount']){
                _productOffersCount = element['productOffersCount'];
              }

              var _productName = "";
              if(element['productDetails'] && element['productDetails']['productName']){
                _productName = element['productDetails']['productName'];
              }

              if(! element['productDetails'] ){
                console.log(element['alt'] +"没有productDetails,忽略！");
                continue;
              }

              if (!isCateNewTask && _buyersQuantity == 0) {
                 console.log(ct(),"当前，零购买的数据");
                 isEnd = true;
              }

                 
                var _productInfo2 = {
                  offerLink: element['url'],
                  offerName: element['alt'],
                  productName: _productName,
                  price: element['price']['mainPrice']['amount'],
                  totalPrice: element['shipping']['itemWithDelivery']['amount'],
                  buyersQuantity: _buyersQuantity,
                  categoryId: element['assortmentCategory']['id'],
                  offerId: element['eventData']['offer_id'],
                  productId: element['productDetails']['productId'],
                  productOffersCount: _productOffersCount,
                  taskId: task.id,
                  imageUrl: element['mainThumbnail'],
                  taskType: task.taskType,
                  sellerId: element['seller']['id'],
                  clientId: msgobj["uniqueId"]+"_"+client_version,
                };
                
                if(isCateNewTask || _buyersQuantity > 0){
                  shangjia_infos_upload.push(_productInfo2);
                }                
              };              
            }          
          // } 

        //   else{

        //   $(".opbox-listing article").each(function () {


        //     var linkElement = $(this).find("h2 a").eq(0);
        //     let _productName = linkElement.text().trim();
        //     var imageUrl ="";

            

            
        //     let _productBuyersCount = 0;

        //     if(isSellerTask){
        //       // let buyersCountSpan = $(this).find('span:contains("osoba kupiła tę ofertę")');
        //       let buyersCountSpan = $(this).find('span:contains("osób")');
        //       let buyersCountText = buyersCountSpan.text().trim();
        //     if (buyersCountText.length) {
        //       let buyersCountMatch = buyersCountText.match(/\d+/);
        //       if (buyersCountMatch) {
        //         _productBuyersCount = parseInt(buyersCountMatch[0], 10);
        //       }        
             

        //     }

        //     let imageUrlLink = $(this).find('img:first');
        //     if(imageUrlLink){
        //         imageUrl = imageUrlLink.attr('src');
        //     }

        //   }else{
        //     let buyersCountSpan = $(this).find('span.mli2_0:contains("ostatnio")').prev('span');
    
            
        //     let buyersCountText = buyersCountSpan.text().trim();
        //     if (buyersCountText.length) {
        //       let buyersCountMatch = buyersCountText.match(/\d+/);
        //       if (buyersCountMatch) {
        //         _productBuyersCount = parseInt(buyersCountMatch[0], 10);
        //       }
        //     }

        //     let imageUrlLink = $(this).find('img[src]');
        //     if(imageUrlLink){
        //         imageUrl = imageUrlLink.attr('src');
        //     }

        //   }

        //   console.log(ct(),_productName + ",购买人数: " + _productBuyersCount);


        //     if (msgobj["collect_zero_sales"] == false &&_productBuyersCount == 0) {
        //       console.log(ct(),_productName + "忽略，商品购买人数: " + _productBuyersCount);
        //       return true;
        //     }

        //     let _productPrice = "未知价格";
        //     var _productTotalPrice = _productPrice;
        //     var hasShipFee = 0;

        //     if(isSellerTask){
        //     let priceContainer = $(this).find('span[aria-label]');
        //     let priceText = priceContainer.text().trim();
        //     let priceRegex = /(\d+,\d+)/;
        //     let priceMatch = priceText.match(priceRegex);            
        //     if (priceMatch) {
        //       _productPrice = parseFloat(priceMatch[1].replace(",", "."));
        //     //  console.log(ct(),"商品价格: " + _productPrice);
        //     } else {
        //       console.log(ct(),"未找到价格信息");
        //     }

        //     _productTotalPrice = _productPrice;
        //     var totalWithShippingContainer = $(this).find('div[class*="mqu1_g3 mgn2_12"]')
        //       .filter(function () {
        //         return $(this).text().trim().includes("zł z dostawą");
        //       });
        //     if (totalWithShippingContainer.length > 0) {
        //       var totalWithShippingText = totalWithShippingContainer.text().trim();
        //       var priceMatchTotal = totalWithShippingText.match(/\d+,\d+/);
        //       if (priceMatchTotal) {
        //         hasShipFee = 1;
        //         _productTotalPrice = parseFloat(
        //           priceMatchTotal[0].replace(",", ".")
        //         );
        //       //  console.log(ct(),"包含运费的总价是: " + _productTotalPrice + " zł");
        //       } else {
        //        // console.log(ct(),"价格格式不匹配");
        //       }
        //     } else {
        //      // console.log(ct(),"未找到包含运费的总价");
        //     }
        //   }else{

        //     let priceContainer = $(this).find('div.m3h2_8 span[aria-label]');
        //     let priceText = priceContainer.text().trim();
        //     let priceRegex = /(\d+,\d+)/;
        //     let priceMatch = priceText.match(priceRegex);
        //     if (priceMatch) {
        //       _productPrice = parseFloat(priceMatch[1].replace(",", "."));
        //     } else {
        //       console.log(ct(),"未找到价格信息");
        //     }

        //      _productTotalPrice = _productPrice;
        //     // var totalWithShippingContainer = $(this).find("div.mj7a_4")
        //     //   .filter(function () {
        //     //     return $(this).text().trim().includes("zł z dostawą");
        //     //   });
        //     var totalWithShippingContainer = $(this).find('div[class*="mqu1_g3 mgn2_12"]')
        //       .filter(function () {
        //         return $(this).text().trim().includes("zł z dostawą");
        //       });

        //     if (totalWithShippingContainer.length > 0) {
        //       var totalWithShippingText = totalWithShippingContainer.text().trim();
        //       var priceMatchTotal = totalWithShippingText.match(/\d+,\d+/);
        //       if (priceMatchTotal) {
        //         hasShipFee = 1;
        //         _productTotalPrice = parseFloat(
        //           priceMatchTotal[0].replace(",", ".")
        //         );
        //       } else {
        //       }
        //     } else {
        //     }
        //   }

        //     let _productLink = linkElement.attr("href");
        //     if (_productLink.indexOf("events/clicks") > 0) {
        //       let url = new URL(_productLink);
        //       let redirectUrl = url.searchParams.get("redirect");
        //       let actualUrl = decodeURIComponent(redirectUrl);
        //       console.log(ct(),"actualUrl:" + actualUrl);
        //       let actualParsedUrl = new URL(actualUrl);
        //       let protocol = actualParsedUrl.protocol + "//";
        //       let hostname = actualParsedUrl.hostname;
        //       _productLink = protocol + hostname + actualParsedUrl.pathname;
        //     }

        //     let _productInfo2 = {
        //       offerLink: _productLink,
        //       offerName: _productName,
        //       imageUrl: imageUrl,
        //       price: _productPrice,
        //       shipFee: _productTotalPrice - _productPrice,
        //       totalPrice: _productTotalPrice,
        //       buyersQuantity: _productBuyersCount,
        //       categoryId: task.categoryId,
        //       taskId: task.id,
        //       taskType: task.taskType,
        //       clientId: msgobj["uniqueId"]+"_"+client_version,
        //     };



        //     if (!isCateNewTask && _productBuyersCount == 0) {
        //       console.log(ct(),"当前，零购买的数据");
        //       isEnd = true;
        //     }

        //     if (!isCateNewTask && _productBuyersCount > 0) {
        //       shangjia_infos_upload.push(_productInfo2);
        //     }

        //     if (isCateNewTask) {
        //       if (_productBuyersCount > 0 || msgobj["collect_zero_sales"]) {
        //         if (
        //           _productTotalPrice >= msgobj["collect_min_price"] &&
        //           _productTotalPrice <= msgobj["collect_max_price"]
        //         ) {
        //           shangjia_infos_upload.push(_productInfo2);
        //         }
        //       }
        //     }

        //     console.log(ct(),_productInfo2);
        //   });

        // }















          if (shangjia_infos_upload.length > 0) {
            var message = {
              action: "shangjia_items_update",
              info: "",
              taskType: task.taskType,
              shangjia_infos: shangjia_infos_upload,
            };
            send_message_to_popup(message);
          }

          if ( !isCateNewTask &&  shangjia_infos_upload.length > 0) {
            isEnd = false;
            console.log(ct(),"当前页面有销售额数据条数:"+shangjia_infos_upload.length+",大于0，准备继续查询下一页。");
          }

          //新增判断页数，当前页是否就是最后一页
          let params = util_getCurrentUrlQueryStringParams();
          console.log(ct(),"params分页：" , params);
          let next_page_num = 2;           
   
        // 增加p的值
        if (params.p) {
          // 如果p存在，增加它的值
          params.p = parseInt(params.p) + 1;
        } else {
          // 如果p不存在，设置它的值为1
          params.p = next_page_num;
        }

        // let max_page_num = $(
        //   "div[data-role='paginationBottom']  span.mvrt_8"
        // ).text();
        let max_page_num = $("div[data-role='paginationBottom'] span._1h7wt.mgmw_wo.mh36_8.mvrt_8").text().trim();


        if (max_page_num) {
          max_page_num = parseInt(max_page_num);
        } else {
          max_page_num = 0;
        }
        console.log(ct(),"最大页数："+max_page_num +",下一页："+params.p );

        if(isCateNewTask){
          //目录新品采集，取max_page_num 和 msgobj["collect_max_page"]  的较小值
          max_page_num = max_page_num > msgobj["collect_max_page"] ? msgobj["collect_max_page"] : max_page_num;
        }

        if(params.p>max_page_num){
          isEnd = true;
          console.warn(ct(),"最大页数："+max_page_num +",下一页："+params.p  +"，已经采集完成。");
        }


        // if ((isSellerTask || isCateTask) && isEnd) {
          if (isEnd) {
            var resp = await uploadCompletedTask();
            // let timeWait = util_get_random_number(20000, 35000);
            let timeWait = util_get_random_number(10000, 15000);

            console.warn(ct(),"当前类别采集结束，准备进入下一页，随机等待3：" + timeWait + "ms");
            var data = await getPendingCatalogUrls(msgobj["uniqueId"]);
            console.log(ct(),"即将跳转页面链接：" + data.link);
            setTimeout(async function () {
              //然后获取数据，进入新的一页              
              location.href = data.link;
            }, timeWait);

            return true;
          }

          console.log(ct(),"继续自动分页采集....");
          let now_url = current_url;
         

      // 重新构建查询字符串
      var other_args = Object.keys(params).map(function(key) {
        // 如果参数值是数组，则需要为每个值生成一个键值对
        if (Array.isArray(params[key])) {
          return params[key].map(function(value) {
            return key + "=" + encodeURIComponent(value);
          }).join('&');
        } else {
          return key + "=" + encodeURIComponent(params[key]);
        }
      }).join('&');

          
          
    

          

          if (next_page_num > msgobj["collect_max_page"]) {
            var message = {
              action: "shangjia_notice",
              info:
                "采集停止，最大页数：" +
                msgobj["collect_max_page"] +
                ",下一页：" +
                next_page_num,
            };
            send_message_to_popup(message);

            var resp = await uploadCompletedTask();

            // let timeWait = util_get_random_number(20000, 35000);
            let timeWait = util_get_random_number(20000, 35000);
            console.log(ct(),"随机等待4：" + timeWait);
            var data = await getPendingCatalogUrls(msgobj["uniqueId"]);
            console.log(ct(),"即将跳转页面链接：" + data.link);
            setTimeout(async function () {
              //然后获取数据，进入新的一页
              location.href = data.link;
            }, timeWait);

            return true;
          }

  

          if (next_page_num <= max_page_num) {
            let now_url_arr = now_url.split("?");
            let next_page_url = now_url_arr[0] + "?" + other_args;
            let timeWait = util_get_random_number(20000, 35000);
            console.log(ct(),"随机等待1：" + timeWait);
            task["next_page_url"] = next_page_url;

            // 使用 Promise 来等待 storage 操作完成
            await new Promise((resolve) => {
              chrome.storage.local.set({ task: task }, resolve);
            });

            setTimeout(async function () {
              window.location.href = next_page_url;
            }, timeWait);
          } else {
            var resp = await uploadCompletedTask();
            // let timeWait = util_get_random_number(20000, 35000);
            let timeWait = util_get_random_number(5000, 10000);
            console.log(ct(),"随机等待2：" + timeWait);
            var data = await getPendingCatalogUrls(msgobj["uniqueId"]);
            console.log(ct(),"即将跳转页面链接：" + data.link);
            setTimeout(async function () {
              //然后获取数据，进入新的一页
            
              location.href = data.link;
            }, timeWait);
          }
        }
      }
    } else {
      console.log(ct(),"==========已暂停自动采集==============");
      console.log(ct(),getCategoryIDFromURL());
    }
  });
}

// 封装请求的方法
async function sendPutRequest(url, jsonMessage, headers, retries = 5) {
  const options = {
    method: "PUT",
    headers: headers,
    body: jsonMessage,
  };

  for (let i = 0; i < retries; i++) {
    try {
      const response = await fetch(url, options);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.warn(`Attempt ${i + 1} failed:`, error);
      if (i < retries - 1) {
        console.log(`Retrying... (${i + 2}/${retries})`);
        await new Promise((res) => setTimeout(res, 20000));
      } else {
        throw error;
      }
    }
  }
}

// 监察页面性能资源加载情况，并根据特定条件记录或执行某些操作。
function perf_observer(list, observer) {
  performanceResourceList = list.getEntriesByType("resource");

  for (i = 0; i < performanceResourceList.length; i++) {
    performanceResourceItem = performanceResourceList[i];

    if (performanceResourceItem["initiatorType"] == "xmlhttprequest") {
      if (
        performanceResourceItem["name"].indexOf("p=") != -1 &&
        performanceResourceItem["name"].indexOf("allegro") != -1 &&
        performanceResourceItem["name"].indexOf("doubleclick") == -1 &&
        performanceResourceItem["name"].indexOf("google-analytics") == -1 &&
        performanceResourceItem["name"].indexOf("ngacm") == -1
      ) {
        console.log("bbbb" + performanceResourceItem["name"]);

        setTimeout(function () {
          // shangjia_search_caiji();
        }, 2000);
      } else if (
        performanceResourceItem["name"].indexOf("p=") != -1 &&
        performanceResourceItem["name"].indexOf("listingId") != -1
      ) {
        console.log("aaaa" + performanceResourceItem["name"]);
        setTimeout(function () {
          // shangjia_search_caiji();
        }, 2000);
      }
    }
  }
}

var observer_ = new PerformanceObserver(perf_observer);
observer_.observe({ entryTypes: ["resource", "navigation", "paint"] });

$(document).ready(function () {
  console.log(ct(),"文档准备就绪时执行的函数 ready");

  chrome.runtime.sendMessage({action: "updateTime"}, response => {
    console.log(ct(),"更新时间响应值：",response.state);
  });

  //判断是否是blocked 页面
  // 获取页面中所有的<script>标签
  var scripts = document.getElementsByTagName('script');
  
  // 检查每个<script>标签的src属性
  for (var i = 0; i < scripts.length; i++) {
    var src = scripts[i].getAttribute('src');
    if (src && src.includes('https://ct.captcha-delivery.com/c.js')) {
     console.warn(ct()+"页面被屏蔽，等待10分钟。");
   
    // 如果找到了匹配的<script>标签，等待10分钟后刷新页面
      setTimeout(function() {
        window.location.reload();
      }, 600000); // 10分钟的毫秒数（600秒 * 1000毫秒）
      return; // 退出循环
    }
    if (src && src.includes('https://assets.allegrostatic.com/skycaptcha/api.js?onload=captchaLoaded')) {
     console.warn(ct()+"页面出现人机验证码被屏蔽，等待10分钟。");
   
    // 如果找到了匹配的<script>标签，等待10分钟后刷新页面
      setTimeout(function() {
        window.location.reload();
      }, 600000); // 10分钟的毫秒数（600秒 * 1000毫秒）
      return; // 退出循环
    }
  }

  // 启动Amazon列表页采集
  setTimeout(function () {
    amazon_list_caiji();
  }, 5000);
});

// 发送消息给弹窗
function send_message_to_popup(message = {}) {
  console.log(ct(),"message");
  console.log(ct(),message);
  chrome.runtime.sendMessage(message, (msg) => {
    if (chrome.runtime.lastError) {
      erpErrLog("消息发送失败:", chrome.runtime.lastError.message);
    }
    // if (message.shangjia_infos.length > 0) {
    //     // alert('该页卖家已成功添加到采集队列');
    // }
    console.log(ct(),"答复：",msg);
  });
}

function downloadIamge(imgsrc, name) {
  let image = new Image();
  image.setAttribute("crossOrigin", "anonymous");
  image.onload = function () {
    let canvas = document.createElement("canvas");
    canvas.width = image.width;
    canvas.height = image.height;
    let context = canvas.getContext("2d");
    context.drawImage(image, 0, 0, image.width, image.height);
    let url = canvas.toDataURL("image/jpeg");
    let a = document.createElement("a");
    let event = new MouseEvent("click");
    a.download = name || "photo";
    a.href = url;
    a.dispatchEvent(event);
  };
  image.src = imgsrc;
}

function get_product_lunbo_imgs() {
  console.log("aaaa");
  $("._2AOclWz7 ._3ACovDZO").each(function () {
    var _img = $(this).find("img").attr("src");
    if (_img.indexOf("?") !== -1) {
      var _imgarr = _img.split("?");
      _img = _imgarr[0];
      _img_src = _img;
      var _imgarr = _img.split("/");
      _img_name = _imgarr[_imgarr.length - 1];
      _img_name_arr = _img_name.split(".");
      _img_name = _img_name_arr[0];
      downloadIamge(_img_src, _img_name);
    }
  });
}

function get_product_details_imgs() {
  console.log("aaaa");
  $("._151rnt-L")
    .find("img")
    .each(function () {
      var _detail_img = $(this).attr("src");
      if (_detail_img.indexOf("?") !== -1) {
        var _detail_imgarr = _detail_img.split("?");
        _detail_img = _detail_imgarr[0];
        _img_src = _detail_img;
        var _imgarr = _detail_img.split("/");
        _img_name = _imgarr[_imgarr.length - 1];
        _img_name_arr = _img_name.split(".");
        _img_name = _img_name_arr[0];
        downloadIamge(_img_src, _img_name);
      }
    });
}

chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action == "extract-product-lunbo-imgs") {
    console.log(ct(),request.info);
    catalog_products_url = get_product_lunbo_imgs();
    sendResponse(catalog_products_url);
  } else if (request.action == "extract-product-details-imgs") {
    console.log(ct(),request.info);
    catalog_products_url = get_product_details_imgs();
    sendResponse(catalog_products_url);  } else if (request.action === "amazon_list_caiji") {
    amazon_list_caiji();
    sendResponse({ status: "amazon_list_caiji 方法已执行" });
  }
  return true;
});

function getCategoryIDFromURL() {
  // 获取当前页面的URL
  const url = window.location.href;
  const urlObj = new URL(url);
  const path = urlObj.pathname;
  // 使用正则表达式匹配类别ID
  const categoryIdMatch = path.match(/kategoria\/.*?-(\d+)/);
  // 如果找到匹配项，返回类别ID；否则返回null
  return categoryIdMatch ? categoryIdMatch[1] : null;
}

// 使用自定义函数解析查询字符串并返回参数对象
function util_getCurrentUrlQueryStringParams() {
  var queryString = window.location.search;
  var params = {};
  queryString = queryString.substring(1);
  var keyValuePairs = queryString.split("&");
  keyValuePairs.forEach(function (keyValuePair) {
    var pair = keyValuePair.split("=");
    var key = decodeURIComponent(pair[0]);
    var value = decodeURIComponent(pair[1] || "");
    
    // 如果params对象中已经存在这个键，则将值添加到数组中
    // 否则，创建一个新数组并添加值
    if (params.hasOwnProperty(key)) {
      if (Array.isArray(params[key])) {
        params[key].push(value);
      } else {
        // 如果之前存储的不是数组，则转换成数组
        params[key] = [params[key], value];
      }
    } else {
      params[key] = value;
    }
  });
  return params;
}


function util_get_random_number(min = 1000, max = 5000) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}


function erpErrLog(msg) {
  console.error(ct()+msg);
  
}

function ct() {
  var now = new Date();
  var year = now.getFullYear();
  var month = now.getMonth() + 1; // 月份是从0开始的
  var day = now.getDate();
  var hours = now.getHours();
  var minutes = now.getMinutes();
  var seconds = now.getSeconds();
  
  // 补零函数
  function pad(number) {
    return (number < 10 ? '0' : '') + number;
  }

  // 返回自定义格式的日期时间字符串
  return year + '-' + pad(month) + '-' + pad(day) + ' ' + pad(hours) + ':' + pad(minutes) + ':' + pad(seconds) +' ';
}

// Amazon页面检测和辅助函数

// 检查是否为Amazon搜索页面
function isAmazonSearchPage(url) {
  return url.includes('amazon.com/s') || url.includes('amazon.com/gp/search');
}

// 检查两个URL是否匹配（忽略页码参数）
function isUrlMatch(currentUrl, taskUrl) {
  const current = new URL(currentUrl);
  const task = new URL(taskUrl);
  
  // 移除页码相关参数
  current.searchParams.delete('page');
  current.searchParams.delete('sr');
  task.searchParams.delete('page');
  task.searchParams.delete('sr');
  
  return current.toString() === task.toString();
}

// 获取当前页码
function getCurrentPageNumber() {
  const urlParams = new URLSearchParams(window.location.search);
  const page = urlParams.get('page');
  return page ? parseInt(page) : 1;
}

// 检查是否有下一页
function hasNextPage() {
  const nextPageLink = document.querySelector('a[aria-label="Go to next page, page 2"], a[aria-label*="next page"], .s-pagination-next');
  return nextPageLink && !nextPageLink.classList.contains('s-pagination-disabled');
}

// 获取下一页URL
function getNextPageUrl() {
  const nextPageLink = document.querySelector('a[aria-label*="next page"], .s-pagination-next:not(.s-pagination-disabled)');
  if (nextPageLink && nextPageLink.href) {
    return nextPageLink.href;
  }
  
  // 如果没找到下一页链接，手动构造
  const currentPage = getCurrentPageNumber();
  const url = new URL(window.location.href);
  url.searchParams.set('page', (currentPage + 1).toString());
  return url.toString();
}
