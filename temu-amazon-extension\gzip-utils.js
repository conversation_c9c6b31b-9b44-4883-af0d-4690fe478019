/**
 * Gzip压缩工具类
 * 支持浏览器原生CompressionStream API和pako库后备方案
 */

class GzipUtils {
  constructor() {
    this.isCompressionStreamSupported = typeof CompressionStream !== 'undefined';
    this.isPakoLoaded = false;
    this.initializePako();
  }

  /**
   * 初始化pako库（如果需要）
   */
  initializePako() {
    // 检查是否已经加载了pako
    if (typeof pako !== 'undefined') {
      this.isPakoLoaded = true;
      return;
    }

    // 如果浏览器不支持CompressionStream，则需要加载pako
    if (!this.isCompressionStreamSupported) {
      console.warn('CompressionStream不支持，需要加载pako库');
      // 这里可以动态加载pako，但为了简单起见，我们先使用内置的简化版本
      this.loadSimplePako();
    }
  }

  /**
   * 加载简化的pako实现
   */
  loadSimplePako() {
    // 这是一个简化的deflate实现，实际项目中建议使用完整的pako库
    window.simplePako = {
      deflate: function(data) {
        // 简单的LZ77压缩算法实现
        return this.simpleCompress(data);
      },
      
      simpleCompress: function(str) {
        // 这是一个非常简化的压缩算法，实际效果有限
        // 建议在生产环境中使用真正的pako库
        let compressed = '';
        let i = 0;
        
        while (i < str.length) {
          let match = this.findLongestMatch(str, i);
          if (match.length > 3) {
            // 找到重复模式，使用引用
            compressed += `[${match.distance},${match.length}]`;
            i += match.length;
          } else {
            // 没有找到重复，直接添加字符
            compressed += str[i];
            i++;
          }
        }
        
        return new Uint8Array(new TextEncoder().encode(compressed));
      },
      
      findLongestMatch: function(str, pos) {
        let maxLength = 0;
        let maxDistance = 0;
        
        // 向前搜索最长匹配
        for (let i = Math.max(0, pos - 1000); i < pos; i++) {
          let length = 0;
          while (pos + length < str.length && 
                 str[i + length] === str[pos + length] && 
                 length < 255) {
            length++;
          }
          
          if (length > maxLength) {
            maxLength = length;
            maxDistance = pos - i;
          }
        }
        
        return { length: maxLength, distance: maxDistance };
      }
    };
    
    this.isPakoLoaded = true;
  }

  /**
   * 使用原生CompressionStream压缩数据
   */
  async compressWithNativeAPI(data) {
    try {
      const stream = new CompressionStream('gzip');
      const writer = stream.writable.getWriter();
      const reader = stream.readable.getReader();
      
      // 写入数据
      const encoder = new TextEncoder();
      await writer.write(encoder.encode(data));
      await writer.close();
      
      // 读取压缩结果
      const chunks = [];
      let done = false;
      
      while (!done) {
        const { value, done: readerDone } = await reader.read();
        done = readerDone;
        if (value) {
          chunks.push(value);
        }
      }
      
      // 合并所有块
      const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
      const result = new Uint8Array(totalLength);
      let offset = 0;
      
      for (const chunk of chunks) {
        result.set(chunk, offset);
        offset += chunk.length;
      }
      
      return result;
    } catch (error) {
      console.error('原生压缩失败:', error);
      throw error;
    }
  }

  /**
   * 使用pako库压缩数据
   */
  compressWithPako(data) {
    try {
      if (typeof pako !== 'undefined') {
        // 使用真正的pako库
        return pako.gzip(data);
      } else if (window.simplePako) {
        // 使用简化版本
        return window.simplePako.deflate(data);
      } else {
        throw new Error('没有可用的压缩库');
      }
    } catch (error) {
      console.error('Pako压缩失败:', error);
      throw error;
    }
  }

  /**
   * 压缩字符串数据
   * @param {string} data 要压缩的字符串
   * @returns {Promise<{compressed: Uint8Array, originalSize: number, compressedSize: number, ratio: number}>}
   */
  async compress(data) {
    const originalSize = new TextEncoder().encode(data).length;
    let compressed;
    let method = '';

    try {
      if (this.isCompressionStreamSupported) {
        compressed = await this.compressWithNativeAPI(data);
        method = 'native';
      } else if (this.isPakoLoaded) {
        compressed = this.compressWithPako(data);
        method = 'pako';
      } else {
        throw new Error('没有可用的压缩方法');
      }

      const compressedSize = compressed.length;
      const ratio = ((originalSize - compressedSize) / originalSize * 100).toFixed(2);

      console.log(`Gzip压缩完成 (${method}):`, {
        originalSize,
        compressedSize,
        ratio: `${ratio}%`
      });

      return {
        compressed,
        originalSize,
        compressedSize,
        ratio: parseFloat(ratio),
        method
      };
    } catch (error) {
      console.error('压缩失败，返回原始数据:', error);
      // 如果压缩失败，返回原始数据
      const encoder = new TextEncoder();
      return {
        compressed: encoder.encode(data),
        originalSize,
        compressedSize: originalSize,
        ratio: 0,
        method: 'none'
      };
    }
  }

  /**
   * 将Uint8Array转换为Base64字符串
   */
  arrayBufferToBase64(buffer) {
    let binary = '';
    const bytes = new Uint8Array(buffer);
    const len = bytes.byteLength;
    
    for (let i = 0; i < len; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    
    return btoa(binary);
  }

  /**
   * 压缩并转换为Base64
   * @param {string} data 要压缩的字符串
   * @returns {Promise<{data: string, isCompressed: boolean, originalSize: number, compressedSize: number, ratio: number}>}
   */
  async compressToBase64(data) {
    const result = await this.compress(data);
    
    return {
      data: this.arrayBufferToBase64(result.compressed),
      isCompressed: result.method !== 'none',
      originalSize: result.originalSize,
      compressedSize: result.compressedSize,
      ratio: result.ratio,
      method: result.method
    };
  }
}

// 创建全局实例
window.gzipUtils = new GzipUtils();

// 导出给其他模块使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = GzipUtils;
}
