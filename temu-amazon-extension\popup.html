<!doctype html>
<!--
This page is shown when the extension button is clicked, because the
"browser_action" field in manifest.json contains the "default_popup" key with value "popup.html".
-->
<html>
<head>
    <title>胡建跨境ERP采集插件</title>
    <meta charset="utf-8" />
  <style>
      *{box-sizing: border-box;}
    [data-tip] {
      position: relative;
    }

    [data-tip]:before {
      content: '';
      /* hides the tooltip when not hovered */
      display: none;
      content: '';
      border-left: 5px solid transparent;
      border-right: 5px solid transparent;
      border-bottom: 5px solid #1a1a1a;
      position: absolute;
      top: 30px;
      left: 35px;
      z-index: 8;
      font-size: 0;
      line-height: 0;
      width: 0;
      height: 0;
    }

    [data-tip]:after {
      display: none;
      content: attr(data-tip);
      position: absolute;
      top: 1px;
      left: 0px;
      padding-left: 2px;
      padding-right: 2px;
      background: #1a1a1a;
      color: #fff;
      z-index: 9;
      font-size: 0.75em;
      height: 20px;
      line-height: 20px;
      -webkit-border-radius: 3px;
      -moz-border-radius: 3px;
      border-radius: 3px;
      white-space: nowrap;
      word-wrap: normal;
      margin-top: 36px;
    }

    [data-tip]:hover:before,
    [data-tip]:hover:after {
      display: block;
    }

    input.model:focus {
      outline-width: 0;
    }

    select:focus {
      outline-width: 0;
    }

    .attachment {
      vertical-align: middle;
      border: #8b8888;
      border-style: solid;
      border-width: 0.05px;
      padding: 3px;
    }

    .modelWrap {
      margin-left: 10px;
      margin-right: 10px;
      width: 97%;
      border-style: ridge;
      border-width: 0.5px;
      border-color: #8b8888;
      background-color: #fff;
      text-align: left;
      padding:3px;
    }

    #overlay {
      position: fixed;
      display: none;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #fff;
      z-index: 2;
      cursor: pointer;
      text-align: center;
      vertical-align: middle;
    }

    .spanWrap {
      color: #fff;
      border-style: ridge;
      border-width: 0;
      background-color: #4fa89cd4;
      text-align: left;
      padding: 1px 3px;
      margin-top: 2px;
      margin-bottom: 2px;
      margin-left: 2px;
      margin-right: 2px;
      border-radius: 4px;
      display: inline-block;
      cursor: default;
    }

    .spanWrapC {
      color: #fff;
      border-style: ridge;
      border-width: 0.5px;
      border-color: #ff5722;
      background-color: #ff5722;
      text-align: left;
      padding: 1px;
      margin-top: 2px;
      margin-bottom: 2px;
      margin-left: 2px;
      margin-right: 2px;
      border-radius: 4px;
      display: inline-block;
      cursor: default;
    }

    .so {
      width: 97%;
      height: 95px;
      margin-left: 10px;
      margin-right: 10px;
      text-align: justify;
      background-color: white;
      font-weight: 4;
      font-size: 15px;
      padding-right: 8%;
      padding-left: 2%;
    }

    .spanWrapInvl {
      color: #fff;
      border-style: ridge;
      border-width: 0.5px;
      border-color: #E53935;
      background-color: #E53935;
      text-align: left;
      padding: 1px;
      margin-top: 2px;
      margin-bottom: 2px;
      margin-left: 2px;
      margin-right: 2px;
      border-radius: 4px;
      display: inline-block;
      cursor: default;
    }

    #countryCode {
      border-style: ridge;
      border-width: 0.5px;
      text-align: left;
      padding: 3px;
      margin-top: 2px;
      margin-bottom: 2px;
      margin-left: 2px;
      margin-right: 2px;
      border-radius: 4px;
      display: inline-block;
      cursor: default;
    }

    .tag {
      padding: 5px;
      background: aliceblue;
      border: deepskyblue 1px solid;
      border-radius: 5px;
    }

    .row {
      display: flex;
    }

    /* Create two equal columns that sits next to each other */
    .column {
      flex: 50%;
      /* Should be removed. Only for demonstration */
    }

    /* Style the tab */
    .tab {
      overflow: hidden;
      background-color: #f1f1f1;
    }

    /* Style the buttons inside the tab */
    .tab button {
      background-color: inherit;
      float: left;
      border: none;
      outline: none;
      cursor: pointer;
      padding: 8px 16px;
      transition: 0.3s;
      font-size: 15px;
    }

    /* Change background color of buttons on hover */
    .tab button:hover {
      background-color: #ddd;
    }

    /* Create an active/current tablink class */
    .tab button.active {
      color: #ff5722;
    }

    /* Style the tab content */
    .tabcontent {
      display: none;
    }
  </style>
  <!--
    - JavaScript and HTML must be in separate files
    -->
  <link rel="stylesheet" href="stylesheet.min.css">
  <link rel="stylesheet" type="text/css" href="popup.css" />
</head>
<body style="text-align: center; width:450px; height: 350px;">
  <div class="header-wrapper">
      <div class="header-top-wrapper">
          <div class="logo-wrapper">
              <div class="logo-desc" style="margin-left: 10px;">胡建跨境ERP采集插件</div>
          </div>
      </div>
  </div>
  <div class="content-wrapper" style="text-align: left;padding-left:10px;padding-right: 10px;">
        <div id="message-info"></div>
        <div id="dvjson"></div>

        <div>
            <div style="margin-bottom: 10px;">
                <div>
                  <button id="opnAllegro" >打开Allegro</button>&nbsp;&nbsp;
                  <button id="extract-catalog-products-clean">清空</button>&nbsp;&nbsp;
                  <!-- <button id="extract-catalog-products-export-clean">导出</button> -->
                  <span id="data_caiji_status" style="display: none ;padding:10px 20px; "></span>
                </div>
               
                <div style="margin: 10px;">
                    类目自动采集：<button id="extract-catalog-products-pause-page" style="display: none;margin-bottom: 8px;">已开启</button> &nbsp;
                    后台采集详情：<button id="extract-catalog-products-pause" style="display: none;">已开启</button><BR />
                    <span style="margin: 15px 0;" id="data_process"></span>
                    <br />
                </div>
                 <form id="collectionSettingsForm" class="container" style="margin-top: 5px; padding: 5px; border: solid 1px #ddd;">
                          
                  <!-- 是否采集零销量 -->
                  <div class="form-group row">
                      <label for="collectZeroSales" class="col-sm-4 col-form-label">是否采集零销量:</label>
                      <div class="col-sm-8">
                          <div class="form-check form-check-inline">
                              <input class="form-check-input" type="radio" name="collectZeroSales" id="optionYes" value="true" >
                              <label class="form-check-label" for="optionYes">是</label>
                              <input class="form-check-input" type="radio" name="collectZeroSales" id="optionNo" value="false" checked>
                              <label class="form-check-label" for="optionNo">否</label>
                          </div>
                      </div>
                  </div>
              
                  <!-- 采集总价区间：最低价，最高价 -->
                  <div class="form-group row">
                      <label for="priceRange" class="col-sm-4 col-form-label">价格区间:</label>
                      <div class="col-sm-4">
                        <span>最低：</span><input type="number" id="minPrice" name="minPrice" style="width: 70px;" class="form-check-inline" placeholder="最低价" min="0">
                      </div>
                      <div class="col-sm-4">
                        <span>最高：</span> <input type="number" id="maxPrice" name="maxPrice" style="width: 70px;" class="form-check-inline" placeholder="最高价" min="0">
                      </div>
                  </div>
              
                  <!-- 类目采集限制：最多 XX条，最多多少页 -->
                  <div class="form-group row">
                      <label for="categoryLimit" class="col-sm-4 col-form-label">采集限制:</label>
                      <div class="col-sm-4">
                        <span>条数：</span><input type="number" id="maxItems" name="maxItems" style="width: 70px;" class="form-check-inline" placeholder="最多条数" min="3000">
                      </div>
                      <div class="col-sm-4">
                        <span>页数：</span><input type="number" id="maxPages" name="maxPages" style="width: 70px;" class="form-check-inline" placeholder="最多页数" min="1" max="100">
                      </div>
                  </div>
                  <div class="form-group row">
                    <label for="categoryLimit" class="col-sm-4 col-form-label">采集标识:</label>
                    <div class="col-sm-4">
                      <span>ID：</span><input type="text" id="uniqueId" name="uniqueId" style="width: 200px;" class="form-check-inline" placeholder="自定义采集标识">
                    </div>
                   
                </div>
              
                  <!-- 提交按钮 -->
                  <div class="form-group row">
                      <div class="col-sm-12 text-right">
                          <button type="button" id="formConfigSubmit" class="btn btn-primary">保存设置</button>
                          <div id="errorMessages" style="color: red;"></div>

                      </div>
                  </div>
              </form>    
             </div>            
        </div>
        <div class="jihuo-wrapper" style="height: 28px;">
            <div class="jihuo" style="height: 28px;display: none;" >
                <form>
                    <input name="active_code" value="" placeholder="输入激活码" style="width: 250px;" id="jihuo_text"/>
                    <input type="button" value="激活" id="jihuo_btn"/>
                </form>
            </div>
          </div>
        <div style="margin-top:10px;" class="footer">
            <div style="display: flex;justify-content: space-between;">
                <div style="font-size: 13px;" >插件版本：<span id="ext_version"></span> </div>
                <div style="font-size: 13px; display: none;">有效期: <span id="expire_date"></span> </div>
                <div style="font-size: 13px;" >ID：<span id="ext_uniqueId"></span> </div>
            </div>
            <div style="display: flex;justify-content: space-between;">
              
            </div>
            
        </div>
  </div>
</body>
<script src="jquerymin.js"></script>
<script src="jquery.md5.js"></script>
<script src="cdnjs.cloudflare.com_ajax_libs_xlsx_0.17.4_xlsx.full.min.js"></script>
<script src="popup.js"></script>
</html>