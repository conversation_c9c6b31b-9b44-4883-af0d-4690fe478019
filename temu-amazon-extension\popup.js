/**
 * 检查当前是否在Amazon页面，如果不是，则打开Amazon页面。
 */
function util_check_is_amazon_page() {
    chrome.tabs.query({ active: true, currentWindow: true }, function (tabs) {
        const currentUrl = tabs[0].url || "";
        if (!currentUrl.includes('amazon.com')) {
            chrome.tabs.create({ url: "https://amazon.com/" });
        }
    });
}



function extract_catalog_products_clean() {
    $('#extract-catalog-products-clean').click(function () {
        if (confirm("确认清空吗?")) {

            chrome.storage.local.get(null, function (items) {
                for (let key in items) {
                  if (key.indexOf("amazon_product_") != -1 ||
                      key.indexOf("amazon_list_") != -1 ||
                      key === "total_add_product_num" ||
                      key === "products"||
                      key === "task") {
                    chrome.storage.local.remove(key, function () {
                      console.log(`键名 ${key} 的值已被清空！`);
                    });
                  }
                }
              });            
        }
    });
}

extract_catalog_products_clean();

/**
 * 处理点击“提取目录产品暂停页面”事件的函数。
 * 无参数。
 * 无返回值。
 */
function extract_catalog_products_pause_page() {
    $('#extract-catalog-products-pause-page').click(async function () {
        // var flag =await checkActiveCode();

        // if(!flag) return;
        chrome.storage.local.get(null, function (items) {
            let prefix = "pause_page";
            let pause_page = 1;
            if (!items.hasOwnProperty(prefix)) {
                pause_page = 1;
            } else {
                pause_page = items[prefix] == 1 ? 0 : 1;
            }
            chrome.storage.local.set({ pause_page: pause_page });

            if (pause_page == 1) {
                $('#extract-catalog-products-pause-page').text('已关闭').css('color', 'red');
            } else {
                $('#extract-catalog-products-pause-page').text('已开启').css('color', 'green');
            }            chrome.tabs.query({
                active: true,
                currentWindow: true
            }, (tabs) => {
                let message = {
                    action: 'amazon_list_caiji',
                    info: ''
                };
                chrome.tabs.sendMessage(tabs[0].id, message, res => {
                    if (chrome.runtime.lastError) {
                        console.error("消息发送失败:", chrome.runtime.lastError.message);
                      }
                    console.log('popup=>content');
                    if (res && res.status) {
                        console.log(res.status); // 输出 '方法已执行'
                    } else {
                        console.log('No status received from content script');
                    }
                });
            });





          });

    });
}

extract_catalog_products_pause_page();

function extract_catalog_products_pause() {
    $('#extract-catalog-products-pause').click(function () {
        util_check_is_maps_page();

        chrome.storage.local.get(null, function (items) {
            let prefix = "pause";
            let pause = 1;
            if (!items.hasOwnProperty(prefix)) {
                pause = 1;
            } else {
                pause = items[prefix] == 1 ? 0 : 1;
            }
            chrome.storage.local.set({ pause: pause });

            if (pause == 1) {
                $('#extract-catalog-products-pause').text('已暂停').css('color', 'red');
            } else {
                $('#extract-catalog-products-pause').text('已开启').css('color', 'green');
            }

          });


    });
}
extract_catalog_products_pause();

function data_process() {


        let total_add_shangjia_num = 0;
        let has_caiji_geren_num = 0;
        let has_caiji_company_num = 0;
        let seller_id_arr = [];
        chrome.storage.local.get(null, (items) => {
          for (let key in items) {
            if (key.indexOf("shangjia_about_contact_and_basic_info_") !== -1) {
              if (items[key]["_is_request"] == 2) {
                has_caiji_geren_num += 1;
                if (items[key]["_seller_id"] && !seller_id_arr.includes(items[key]["_seller_id"])) {
                  seller_id_arr.push(items[key]["_seller_id"]);
                }
              }
            }
            if (items.hasOwnProperty("total_add_shangjia_num")) {
              total_add_shangjia_num = items["total_add_shangjia_num"];
            }
          }
          let obj = {
            data_process: {
              total_add_shangjia_num: total_add_shangjia_num,
              has_caiji_geren_num: has_caiji_geren_num,
              has_caiji_company_num: has_caiji_company_num,
              has_caiji_seller_num: seller_id_arr.length
            }
          };
          chrome.storage.local.set(obj, () => {
            // 数据更新后，可能需要在这里调用回调或更新 UI        
  
      
              let statusText = "采 集 状 态：<span style='color:green'><b>采集中</b></span>";
             
      
              $('#data_caiji_status').html(statusText);
              $('#data_process').html("商品已采集：<span style='color:green'><strong>" + total_add_shangjia_num + "</strong></span>");
           
          });
        });
     

}
data_process();

setInterval(data_process, 3000);

var activeCode = "";
var uniqueId = "";



function init() {

    chrome.storage.local.get(null, function (callback_data){
        
        if (callback_data.hasOwnProperty('pause') && callback_data['pause'] == 1) {
            $('#extract-catalog-products-pause').text('已暂停').css('color', 'red');
        } else {
            $('#extract-catalog-products-pause').text('已开启').css('color', 'green');
        }
        $('#extract-catalog-products-pause').show();
        //自动翻页采集
        if (callback_data['pause_page'] == 1) {
            $('#extract-catalog-products-pause-page').text('已关闭').css('color', 'red');
        } else {
            $('#extract-catalog-products-pause-page').text('已开启').css('color', 'green');
        }

        if (callback_data.hasOwnProperty('uniqueId') && callback_data['uniqueId'] != '') {
            uniqueId = callback_data['uniqueId'];
            $("#ext_uniqueId").html(uniqueId);
        }
        if (callback_data.hasOwnProperty('activeCode') && callback_data['activeCode'] != '') {
            activeCode = callback_data['activeCode'];
        }







    });

     // 定义默认值
     const defaultValues = {
        collect_zero_sales: true, // 假设默认 采集零销量
        collect_min_price: 30,       // 最低价格默认为0
        collect_max_price: 500,    // 最高价格默认为1000
        collect_item_num: 10000,     // 最多半数条目默认为3000
        collect_max_page: 100,        // 最大页数默认为50
        expire: '2025-01-01',
        uniqueId: '未知'
    };

    // 从本地存储中获取数据，如果没有则赋予默认值
    chrome.storage.local.get(['collect_zero_sales', 'collect_min_price', 'collect_max_price', 'collect_item_num', 'collect_max_page','uniqueId'], function (result) {
        // 检查每个值是否存在，如果不存在则使用默认值
        for (let key in defaultValues) {
            if (result[key] === undefined) {
                result[key] = defaultValues[key];
                // 保存默认值到本地存储
                chrome.storage.local.set({ [key]: result[key] });
            }
        }

        // 更新页面上的输入元素
        // 零销量采集选项
        document.getElementById('optionYes').checked = result.collect_zero_sales === true;
        document.getElementById('optionNo').checked = result.collect_zero_sales !== true;

        // 价格区间
        document.getElementById('minPrice').value = result.collect_min_price;
        document.getElementById('maxPrice').value = result.collect_max_price;

        // 采集限制
        document.getElementById('maxItems').value = result.collect_item_num;
        document.getElementById('maxPages').value = result.collect_max_page;
        document.getElementById('uniqueId').value = result.uniqueId;
    });

    // popup ---> content
    chrome.tabs.query({
        active: true,
        currentWindow: true
    }, (tabs) => {
        let message = {
            action: 'shangjia_search_caiji',
            info: ''
        }
        chrome.tabs.sendMessage(tabs[0].id, message, res => {
            if (chrome.runtime.lastError) {
                console.error("消息发送失败:", chrome.runtime.lastError.message);
              }
            console.log('popup=>content');
            if (res && res.status) {
                console.log(res.status); // 输出 '方法已执行'
            } else {
                console.log('No status received from content script');
            }
        })
    })

    $('#ext_version').html(chrome.runtime.getManifest().version);
    $('#extract-catalog-products-pause-page').show();
}
init();



function formConfigSubmit() {
    $('#formConfigSubmit').click(async function () {
        // 获取表单值
        var selectedValue = $('input[name="collectZeroSales"]:checked').val();
        const collectZeroSales = selectedValue == "true";
        const minPrice = parseFloat(document.getElementById('minPrice').value);
        const maxPrice = parseFloat(document.getElementById('maxPrice').value);
        const maxItems = parseInt(document.getElementById('maxItems').value, 10);
        const maxPages = parseInt(document.getElementById('maxPages').value, 10);
        const uniqueId = document.getElementById('uniqueId').value;

        // 错误信息容器
        $('#errorMessages').html('');

        // 数据验证
        let isValid = true;
        const errors = [];

        if (isNaN(minPrice) || minPrice < 0) {
            errors.push('最低价必须是大于等于0的数字。');
            isValid = false;
        }
        if (isNaN(maxPrice) || maxPrice < 0) {
            errors.push('最高价必须是大于等于0的数字。');
            isValid = false;
        }
        if (minPrice >= maxPrice) {
            errors.push('最低价必须小于最高价。');
            isValid = false;
        }
        if (isNaN(maxItems) || maxItems > 10000) {
            errors.push('最高条目数必须小于或等于10000。');
            isValid = false;
        }
        if (isNaN(maxPages) || maxPages > 100) {
            errors.push('最高页数必须小于或等于100。');
            isValid = false;
        }
        if (!isValid) {
            $('#errorMessages').html(errors.join('<br>'));
            return;
        }

        $("#ext_uniqueId").html(uniqueId);


        // 保存到本地存储
        chrome.storage.local.set({
            collect_zero_sales: collectZeroSales,
            collect_min_price: minPrice,
            collect_max_price: maxPrice,
            collect_item_num: maxItems,
            collect_max_page: maxPages,
            uniqueId: uniqueId
        }, function () {
            $('#errorMessages').html('设置已保存。');
        });
    });
}

formConfigSubmit();

//从content接收信息
chrome.runtime.onMessage.addListener((req, send, sendRes) => {
    console.log("收到info:", req.info)
    console.log("收到action:", req.action)
    console.log("收到tbm_map_urls:", req.maijia_num)

    if (req.action == 'update-shangjia-num') {
        var catalog_products_num = 0;
        var maijia_num = req.maijia_num;
        $('.catalog-products-num').html(maijia_num);
    } else if (req.action == 'background-tips') {
        $('#message-info').html("<div style='color:red'>" + req.info + "</div>");
        chrome.tabs.create({ url: req.url });
    } else if (req.action == 'shangjia_notice') {
        $('#message-info').html("<div style='color:red'>" + req.info + "</div>");
    }

    return true;

    //sendRes("popup已经收到")
});


// 创建一个连接到后台的端口
var backgroundPort = chrome.runtime.connect({ name: "popup" });

// 监听来自后台脚本的消息
backgroundPort.onMessage.addListener(function(msg) {
    if (chrome.runtime.lastError) {
        console.error(chrome.runtime.lastError.message);
        return;
    }
    if (msg.action === "background-tips") {
        console.log("Popup received message:", msg.info);
        // 根据消息更新UI
        $('#message-info').html(`<div style='color:red'>${msg.info}</div>`);
        if (msg.url) {
            chrome.tabs.create({ url: msg.url });
        }
    }
});


// 打开Facebook页面
document.addEventListener('DOMContentLoaded', function() {

    $('#opnAllegro').click(function () {
        chrome.tabs.create({ url: "https://allegro.pl/" });
    });

});



