<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gzip压缩测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result-area {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .compression-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
        }
        textarea {
            width: 100%;
            height: 150px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗜️ Gzip压缩测试工具</h1>
        <p>此页面用于测试Amazon采集插件的gzip压缩功能。</p>
        
        <div class="test-section">
            <h3>📋 压缩工具状态</h3>
            <div id="gzip-status">
                <div>Gzip工具: <span id="gzip-tool-status" class="status">检查中...</span></div>
                <div>CompressionStream API: <span id="compression-api-status" class="status">检查中...</span></div>
                <div>Pako库: <span id="pako-status" class="status">检查中...</span></div>
            </div>
            <button onclick="window.gzipTestFunctions.checkGzipStatus()">重新检查</button>
        </div>

        <div class="test-section">
            <h3>🧪 压缩测试</h3>
            <div>
                <label for="test-data">测试数据:</label>
                <textarea id="test-data" placeholder="输入要压缩的文本数据...">
这是一个测试字符串，用于验证gzip压缩功能。
重复的内容可以获得更好的压缩效果。
重复的内容可以获得更好的压缩效果。
重复的内容可以获得更好的压缩效果。
Amazon搜索结果页面通常包含大量重复的HTML结构，非常适合gzip压缩。
Amazon搜索结果页面通常包含大量重复的HTML结构，非常适合gzip压缩。
Amazon搜索结果页面通常包含大量重复的HTML结构，非常适合gzip压缩。
                </textarea>
            </div>
            <button onclick="window.gzipTestFunctions.testCompression()">测试压缩</button>
            <button onclick="window.gzipTestFunctions.loadSampleHtml()">加载示例HTML</button>
            <button onclick="window.gzipTestFunctions.clearTestData()">清空数据</button>
        </div>

        <div class="test-section">
            <h3>📊 压缩统计</h3>
            <div class="compression-stats" id="compression-stats">
                <div class="stat-card">
                    <div class="stat-value" id="original-size">-</div>
                    <div class="stat-label">原始大小 (bytes)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="compressed-size">-</div>
                    <div class="stat-label">压缩后大小 (bytes)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="compression-ratio">-</div>
                    <div class="stat-label">压缩比 (%)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="compression-method">-</div>
                    <div class="stat-label">压缩方法</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>📝 压缩结果</h3>
            <div id="compression-result" class="result-area">
                压缩结果将显示在这里...
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 Amazon HTML测试</h3>
            <p>测试从Amazon页面提取的HTML内容压缩效果</p>
            <button onclick="window.gzipTestFunctions.testAmazonHtml()">测试Amazon HTML压缩</button>
            <button onclick="window.gzipTestFunctions.simulateUpload()">模拟上传测试</button>
            <button onclick="window.gzipTestFunctions.useDebugHelper()">使用调试助手</button>
        </div>
    </div>

    <!-- 引入外部JavaScript文件 -->
    <script src="gzip-utils.js"></script>
    <script src="test-gzip.js"></script>
</body>
</html>
