/**
 * Gzip测试页面的JavaScript代码
 * 分离到外部文件以避免CSP问题
 */

// 更新状态显示
function updateStatus(elementId, status, text) {
    const element = document.getElementById(elementId);
    if (element) {
        element.className = `status ${status}`;
        element.textContent = text;
    }
}

// 检查gzip工具状态
function checkGzipStatus() {
    console.log('检查Gzip工具状态...');

    // 检查gzip工具
    if (typeof window.gzipUtils !== 'undefined') {
        updateStatus('gzip-tool-status', 'success', '✅ 已加载');
    } else {
        updateStatus('gzip-tool-status', 'error', '❌ 未加载');
    }

    // 检查CompressionStream API
    if (typeof CompressionStream !== 'undefined') {
        updateStatus('compression-api-status', 'success', '✅ 支持');
    } else {
        updateStatus('compression-api-status', 'error', '❌ 不支持');
    }

    // 检查Pako库
    if (typeof pako !== 'undefined') {
        updateStatus('pako-status', 'success', '✅ 已加载');
    } else if (typeof window.simplePako !== 'undefined') {
        updateStatus('pako-status', 'info', '⚠️ 简化版本');
    } else {
        updateStatus('pako-status', 'error', '❌ 未加载');
    }
}

// 测试压缩功能
async function testCompression() {
    const testData = document.getElementById('test-data').value;
    if (!testData.trim()) {
        alert('请输入测试数据');
        return;
    }

    if (typeof window.gzipUtils === 'undefined') {
        alert('Gzip工具未加载');
        return;
    }

    try {
        console.log('开始压缩测试...');
        const result = await window.gzipUtils.compressToBase64(testData);
        
        // 更新统计信息
        document.getElementById('original-size').textContent = result.originalSize.toLocaleString();
        document.getElementById('compressed-size').textContent = result.compressedSize.toLocaleString();
        document.getElementById('compression-ratio').textContent = result.ratio.toFixed(2) + '%';
        document.getElementById('compression-method').textContent = result.method;

        // 显示压缩结果
        const resultArea = document.getElementById('compression-result');
        resultArea.innerHTML = `
            <strong>压缩成功!</strong><br>
            原始大小: ${result.originalSize} bytes<br>
            压缩后大小: ${result.compressedSize} bytes<br>
            压缩比: ${result.ratio.toFixed(2)}%<br>
            压缩方法: ${result.method}<br>
            是否压缩: ${result.isCompressed ? '是' : '否'}<br><br>
            <strong>Base64压缩数据 (前200字符):</strong><br>
            ${result.data.substring(0, 200)}${result.data.length > 200 ? '...' : ''}
        `;

        console.log('压缩测试完成:', result);
    } catch (error) {
        console.error('压缩测试失败:', error);
        document.getElementById('compression-result').innerHTML = `
            <strong style="color: red;">压缩失败!</strong><br>
            错误信息: ${error.message}
        `;
    }
}

// 加载示例HTML
function loadSampleHtml() {
    const sampleHtml = `
<div data-component-type="s-search-results">
    <div class="s-result-item" data-asin="B08N5WRWNW">
        <h3 class="s-size-mini s-spacing-none s-color-base">
            <a href="/dp/B08N5WRWNW">Apple MacBook Air</a>
        </h3>
        <span class="a-price-whole">999</span>
        <span class="a-price-fraction">00</span>
    </div>
    <div class="s-result-item" data-asin="B08N5WRWNW">
        <h3 class="s-size-mini s-spacing-none s-color-base">
            <a href="/dp/B08N5WRWNW">Apple MacBook Air</a>
        </h3>
        <span class="a-price-whole">999</span>
        <span class="a-price-fraction">00</span>
    </div>
</div>
        `.repeat(10); // 重复10次以增加数据量

    document.getElementById('test-data').value = sampleHtml;
}

// 清空测试数据
function clearTestData() {
    document.getElementById('test-data').value = '';
    document.getElementById('compression-result').innerHTML = '压缩结果将显示在这里...';
    
    // 重置统计信息
    document.getElementById('original-size').textContent = '-';
    document.getElementById('compressed-size').textContent = '-';
    document.getElementById('compression-ratio').textContent = '-';
    document.getElementById('compression-method').textContent = '-';
}

// 测试Amazon HTML压缩
async function testAmazonHtml() {
    // 模拟Amazon搜索结果HTML
    const amazonHtml = generateMockAmazonHtml();
    document.getElementById('test-data').value = amazonHtml;
    await testCompression();
}

// 生成模拟Amazon HTML
function generateMockAmazonHtml() {
    const products = [];
    for (let i = 0; i < 50; i++) {
        products.push(`
        <div class="s-result-item" data-asin="B08N5WRWN${i}">
            <div class="s-image">
                <img src="https://m.media-amazon.com/images/I/product${i}.jpg" alt="Product ${i}">
            </div>
            <div class="s-item-container">
                <h3 class="s-size-mini s-spacing-none s-color-base">
                    <a href="/dp/B08N5WRWN${i}">Product Title ${i} - High Quality Item with Great Features</a>
                </h3>
                <div class="a-row a-size-base">
                    <span class="a-price-whole">${Math.floor(Math.random() * 1000)}</span>
                    <span class="a-price-fraction">${Math.floor(Math.random() * 100)}</span>
                </div>
                <div class="a-row">
                    <span class="a-icon-alt">4.${Math.floor(Math.random() * 10)} out of 5 stars</span>
                    <span class="a-size-base">(${Math.floor(Math.random() * 1000)} reviews)</span>
                </div>
            </div>
        </div>
        `);
    }
    
    return `<div data-component-type="s-search-results">${products.join('')}</div>`;
}

// 模拟上传测试
async function simulateUpload() {
    const testData = document.getElementById('test-data').value;
    if (!testData.trim()) {
        alert('请先加载测试数据');
        return;
    }

    try {
        console.log('模拟上传测试...');
        
        // 模拟content.js中的处理流程
        const result = await window.gzipUtils.compressToBase64(testData);
        
        const payload = {
            id: 'test-task-123',
            url: 'https://www.amazon.com/s?k=laptop',
            htmlContent: result.data,
            isCompressed: result.isCompressed,
            compressionInfo: {
                originalSize: result.originalSize,
                compressedSize: result.compressedSize,
                ratio: result.ratio,
                method: result.method
            },
            currentPage: 1,
            totalPages: 10
        };

        const payloadSize = new TextEncoder().encode(JSON.stringify(payload)).length;
        
        document.getElementById('compression-result').innerHTML = `
            <strong>模拟上传测试完成!</strong><br><br>
            <strong>压缩信息:</strong><br>
            原始HTML大小: ${result.originalSize.toLocaleString()} bytes<br>
            压缩后大小: ${result.compressedSize.toLocaleString()} bytes<br>
            压缩比: ${result.ratio.toFixed(2)}%<br>
            压缩方法: ${result.method}<br><br>
            <strong>上传负载:</strong><br>
            总负载大小: ${payloadSize.toLocaleString()} bytes (${(payloadSize/1024).toFixed(2)} KB)<br>
            是否压缩: ${result.isCompressed ? '是' : '否'}<br><br>
            <strong>节省的带宽:</strong><br>
            ${result.isCompressed ? 
                `节省了 ${(result.originalSize - result.compressedSize).toLocaleString()} bytes (${result.ratio.toFixed(2)}%)` :
                '未使用压缩'
            }
        `;

        console.log('模拟上传完成:', { payload, payloadSize });
    } catch (error) {
        console.error('模拟上传失败:', error);
        document.getElementById('compression-result').innerHTML = `
            <strong style="color: red;">模拟上传失败!</strong><br>
            错误信息: ${error.message}
        `;
    }
}

// 使用调试助手进行测试
function useDebugHelper() {
    if (typeof window.debugAmazonCrawler !== 'undefined') {
        console.log('🔧 使用调试助手进行测试...');
        console.log('可用命令:');
        console.log('  debugAmazonCrawler.testGzip() - 测试Gzip压缩');
        console.log('  debugAmazonCrawler.testPageCompression() - 测试当前页面压缩');
        console.log('  debugAmazonCrawler.testSpuSku() - 测试SPU/SKU提取');
        
        // 自动运行一些测试
        window.debugAmazonCrawler.testGzip();
    } else {
        alert('调试助手未加载，请在Amazon页面使用此功能');
    }
}

// 页面加载时自动检查状态
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(checkGzipStatus, 1000);
});

// 导出函数供HTML页面使用
window.gzipTestFunctions = {
    checkGzipStatus,
    testCompression,
    loadSampleHtml,
    clearTestData,
    testAmazonHtml,
    simulateUpload,
    useDebugHelper
};
